package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import com.zhentao.mapper.*;
import com.zhentao.pojo.*;
import com.zhentao.service.SysRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public Result findAllRole() {
        List<SysRole> sysRoles = sysRoleMapper.selectList(null);
        if (sysRoles!=null && !sysRoles.isEmpty()){
            for (SysRole sysRole : sysRoles) {
                int count=0;
                QueryWrapper<SysUserRole> sysUserRoleQueryWrapper=new QueryWrapper<>();
                sysUserRoleQueryWrapper.eq("role_id",sysRole.getRoleId());
                List<SysUserRole> userRoleList = sysUserRoleMapper.selectList(sysUserRoleQueryWrapper);
                for (SysUserRole sysUserRole : userRoleList) {
                    Integer userId = sysUserRole.getUserId();
                    QueryWrapper<SysUser> sysUserQueryWrapper=new QueryWrapper<>();
                    sysUserQueryWrapper.eq("user_id",userId);
                    sysUserQueryWrapper.eq("del_flag",0);
                    SysUser sysUser = sysUserMapper.selectOne(sysUserQueryWrapper);
//                    System.err.println(sysUser.toString());
                    if (sysUser!=null){
                        count+=1;
                    }
                }
                sysRole.setUserCount(count);

                //查询角色关联的权限ID,通过sys_role_menu表关联查询
                QueryWrapper<SysRoleMenu> sysRoleMenuQueryWrapper=new QueryWrapper<>();
                sysRoleMenuQueryWrapper.eq("role_id",sysRole.getRoleId());
                List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(sysRoleMenuQueryWrapper);
                List<Map<String, Object>> permissionId = new ArrayList<>();
                for (SysRoleMenu sysRoleMenu : sysRoleMenus) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("menuId",+sysRoleMenu.getMenuId());
                    permissionId.add(map);
                }
                sysRole.setPermissionIds(permissionId);

            }
            return Result.OK(sysRoles);
        }else {
            return Result.ERROR("查询失败");
        }
    }

    @Override
    public SysRole findRoleById(Integer roleId) {
        SysRole sysRole = sysRoleMapper.selectById(roleId);
        if (sysRole!=null){
            // 计算关联用户数量
            int count = 0;
            QueryWrapper<SysUserRole> sysUserRoleQueryWrapper = new QueryWrapper<>();
            sysUserRoleQueryWrapper.eq("role_id", roleId);
            List<SysUserRole> userRoleList = sysUserRoleMapper.selectList(sysUserRoleQueryWrapper);
            for (SysUserRole sysUserRole : userRoleList) {
                Integer userId = sysUserRole.getUserId();
                QueryWrapper<SysUser> sysUserQueryWrapper = new QueryWrapper<>();
                sysUserQueryWrapper.eq("user_id", userId);
                sysUserQueryWrapper.eq("del_flag", 0);
                SysUser sysUser = sysUserMapper.selectOne(sysUserQueryWrapper);
                if (sysUser != null) {
                    count += 1;
                }
            }
            sysRole.setUserCount(count);
            
            // 查询角色关联的权限
            QueryWrapper<SysRoleMenu> sysRoleMenuQueryWrapper=new QueryWrapper<>();
            sysRoleMenuQueryWrapper.eq("role_id",roleId);
            List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(sysRoleMenuQueryWrapper);
            List<Map<String, Object>> permissionId = new ArrayList<>();
            for (SysRoleMenu sysRoleMenu : sysRoleMenus) {
                Map<String, Object> map = new HashMap<>();
                map.put("menuId",+sysRoleMenu.getMenuId());
                permissionId.add(map);
            }
            sysRole.setPermissionIds(permissionId);
            return sysRole;
        }else {
            return null;
        }
    }

    @Override
    public Result findAllPermission() {
        List<SysMenu> sysMenus = sysMenuMapper.selectList(null);
        if (sysMenus!=null){
            return Result.OK(sysMenus);
        }else {
            return Result.ERROR("查询失败");
        }
    }

    @Override
    public Result findRolePermission(Integer roleId) {
        QueryWrapper<SysRoleMenu> sysRoleMenuQueryWrapper=new QueryWrapper<>();
        sysRoleMenuQueryWrapper.eq("role_id",roleId);
        List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(sysRoleMenuQueryWrapper);
        if (sysRoleMenus!=null && !sysRoleMenus.isEmpty()){
            //用于存储权限id,避免重复（如果有需要去重的情况）
            Set<Integer> menuIdSet = new HashSet<>();
            for (SysRoleMenu sysRoleMenu : sysRoleMenus) {
                Integer menuId = sysRoleMenu.getMenuId();
                menuIdSet.add(menuId);
            }
            // 3.根据菜单ID查询菜单信息（sys_menu），提取perms作为权限
            QueryWrapper<SysMenu> sysMenuQueryWrapper=new QueryWrapper<>();
            sysMenuQueryWrapper.in("menu_id",menuIdSet);
            List<SysMenu> sysMenus = sysMenuMapper.selectList(sysMenuQueryWrapper);
            //4.收集权限（perms字段）,去重后返回
            Set<String> permissions=new HashSet<>();
            for (SysMenu sysMenu : sysMenus) {
                String perms = sysMenu.getPerms();
                permissions.add(perms);
            }
            return Result.OK(permissions);
        }
        return Result.ERROR("该角色未关联任何菜单权限");
    }

    @Override
    @Transactional
    public Result addRole(SysRole sysRole) {
        // 1. 检查角色名称和编码是否已存在
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", sysRole.getRoleName())
                .or()
                .eq("role_key", sysRole.getRoleKey());
        
        SysRole existingRole = sysRoleMapper.selectOne(queryWrapper);
        if (existingRole != null) {
            if (existingRole.getRoleName().equals(sysRole.getRoleName())) {
                return Result.ERROR("角色名称已存在");
            } else {
                return Result.ERROR("角色编码已存在");
            }
        }
        
        // 2. 设置角色基本信息
        sysRole.setStatus(0); // 默认状态为正常
        sysRole.setCreateBy(UserContext.getCurrentUser().getUserId());
        sysRole.setCreateTime(new Date());
        sysRole.setRoleSort(99); // 设置默认排序值
        
        // 3. 保存角色基本信息
        int result = sysRoleMapper.insert(sysRole);
        
        if (result > 0) {
            return Result.OK("添加角色成功");
        } else {
            return Result.ERROR("添加角色失败");
        }
    }

    @Override
    @Transactional
    public Result updateRole(SysRole sysRole) {
        // 1. 检查角色是否存在
        SysRole existingRole = sysRoleMapper.selectById(sysRole.getRoleId());
        if (existingRole == null) {
            return Result.ERROR("角色不存在");
        }
        
        // 2. 检查角色名称和编码是否与其他角色冲突
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("role_id", sysRole.getRoleId())
                .and(wrapper -> wrapper.eq("role_name", sysRole.getRoleName())
                        .or()
                        .eq("role_key", sysRole.getRoleKey()));
        
        SysRole conflictingRole = sysRoleMapper.selectOne(queryWrapper);
        if (conflictingRole != null) {
            if (conflictingRole.getRoleName().equals(sysRole.getRoleName())) {
                return Result.ERROR("角色名称已被其他角色使用");
            } else {
                return Result.ERROR("角色编码已被其他角色使用");
            }
        }
        
        // 3. 设置角色更新信息
        sysRole.setUpdateBy(UserContext.getCurrentUser().getUserId());
        sysRole.setUpdateTime(new Date());
        
        // 4. 更新角色信息
        int result = sysRoleMapper.updateById(sysRole);
        
        if (result > 0) {
            return Result.OK("更新角色成功");
        } else {
            return Result.ERROR("更新角色失败");
        }
    }

    @Override
    @Transactional
    public Result deleteRole(Integer roleId) {
        // 1. 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(roleId);
        if (role == null) {
            return Result.ERROR("角色不存在");
        }
        
        // 2. 检查是否有用户关联此角色
        QueryWrapper<SysUserRole> userRoleWrapper = new QueryWrapper<>();
        userRoleWrapper.eq("role_id", roleId);
        Long count = sysUserRoleMapper.selectCount(userRoleWrapper);
        
        if (count > 0) {
            return Result.ERROR("该角色下有关联用户，请先取消关联再删除");
        }
        
        // 3. 删除角色菜单关联
        QueryWrapper<SysRoleMenu> roleMenuWrapper = new QueryWrapper<>();
        roleMenuWrapper.eq("role_id", roleId);
        sysRoleMenuMapper.delete(roleMenuWrapper);
        
        // 4. 删除角色
        int result = sysRoleMapper.deleteById(roleId);
        
        if (result > 0) {
            return Result.OK("删除角色成功");
        } else {
            return Result.ERROR("删除角色失败");
        }
    }

    @Override
    @Transactional
    public Result updateRolePermission(Integer roleId, List<Integer> menuIds) {
        // 1. 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(roleId);
        if (role == null) {
            return Result.ERROR("角色不存在");
        }
        
        try {
            // 2. 删除原有的角色-菜单关联
            QueryWrapper<SysRoleMenu> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("role_id", roleId);
            sysRoleMenuMapper.delete(deleteWrapper);
            
            // 3. 如果有新的权限，添加新的角色-菜单关联
            if (menuIds != null && !menuIds.isEmpty()) {
                for (Integer menuId : menuIds) {
                    SysRoleMenu roleMenu = new SysRoleMenu();
                    roleMenu.setRoleId(roleId);
                    roleMenu.setMenuId(menuId);
                    sysRoleMenuMapper.insert(roleMenu);
                }
            }
            
            return Result.OK("更新角色权限成功");
        } catch (Exception e) {
            // 出现异常时事务会回滚
            return Result.ERROR("更新角色权限失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result findRoleUsers(Integer roleId) {
        // 1. 检查角色是否存在
        SysRole role = sysRoleMapper.selectById(roleId);
        if (role == null) {
            return Result.ERROR("角色不存在");
        }
        
        try {
            // 2. 查询与该角色关联的用户角色关系
            QueryWrapper<SysUserRole> userRoleWrapper = new QueryWrapper<>();
            userRoleWrapper.eq("role_id", roleId);
            List<SysUserRole> userRoleList = sysUserRoleMapper.selectList(userRoleWrapper);
            
            if (userRoleList.isEmpty()) {
                return Result.OK(new ArrayList<>());
            }
            
            // 3. 提取用户ID
            List<Integer> userIds = userRoleList.stream()
                    .map(SysUserRole::getUserId)
                    .collect(Collectors.toList());
            
            // 4. 查询用户详情
            QueryWrapper<SysUser> userWrapper = new QueryWrapper<>();
            userWrapper.in("user_id", userIds);
            userWrapper.eq("del_flag", 0); // 只查询未删除的用户
            List<SysUser> userList = sysUserMapper.selectList(userWrapper);
            
            return Result.OK(userList);
        } catch (Exception e) {
            return Result.ERROR("查询角色关联用户失败: " + e.getMessage());
        }
    }
}
