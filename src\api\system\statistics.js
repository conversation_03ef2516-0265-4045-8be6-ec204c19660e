import request from '@/request';

/**
 * 获取班级积分概览数据
 * @returns {Promise}
 */
export function getClassPointsOverview() {
  return request({
    url: '/statistics/class-overview',
    method: 'get'
  });
}

/**
 * 获取班级积分排名
 * @returns {Promise}
 */
export function getClassRankings() {
  return request({
    url: '/statistics/class-rankings',
    method: 'get'
  });
}

/**
 * 获取班级积分趋势
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getClassPointsTrend(params) {
  return request({
    url: '/statistics/class-trend',
    method: 'get',
    params
  });
}

/**
 * 获取班级积分分布
 * @returns {Promise}
 */
export function getClassPointsDistribution() {
  return request({
    url: '/statistics/class-distribution',
    method: 'get'
  });
}

/**
 * 获取班级积分详情列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getClassPointsDetails(params) {
  return request({
    url: '/statistics/class-details',
    method: 'get',
    params
  });
}