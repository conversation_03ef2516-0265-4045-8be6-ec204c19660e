<template>
  <div>
    <el-card>
      <el-form :inline="true" :model="searchForm" class="search-form" style="margin-bottom: 16px">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.applicationName" placeholder="请输入姓名" clearable />
        </el-form-item>
        <!--        <el-form-item label="手机号">-->
        <!--          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />-->
        <!--        </el-form-item>-->
        <el-form-item label="班级">
          <el-input v-model="searchForm.className" placeholder="请输入班级" clearable />
        </el-form-item>
        <el-form-item label="活动">
          <el-select v-model="searchForm.activityId" placeholder="请选择活动" style="width: 240px" clearable>
            <el-option
              v-for="item in activityList"
              :key="item.id"
              :label="item.activityName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 260px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="applyList" style="width: 100%">
<!--        <el-table-column prop="id" label="活动ID" />-->
        <el-table-column prop="name" label="报名人" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="className" label="班级" />
        <el-table-column prop="createTime" label="报名时间" />
        <el-table-column prop="activityName" label="报名活动" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" type="danger" @click="cancel(row)">取消报名</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { activityApplicationPage, cancelSignUpActivity } from '@/api/system/activityApplication';
  import { findActivity } from '@/api/system/activity.js';

  const applyList = ref([]);
  const searchForm = ref({ applicationName: '', className: '', activityId: '', timeRange: [] });
  const activityList = ref([]);
  const findActivityList = () => {
    findActivity().then((res) => {
      activityList.value = res.data.data || [];
    });
  };
  findActivityList();
  function getList() {
    // 只传递有值的字段
    const params = { pageCurrent: 1, pageSize: 20 };
    if (searchForm.value.applicationName) params.applicationName = searchForm.value.applicationName;
    if (searchForm.value.className) params.className = searchForm.value.className;
    if (searchForm.value.activityId) params.activityId = searchForm.value.activityId;
    if (searchForm.value.timeRange && searchForm.value.timeRange.length === 2) params.timeRange = searchForm.value.timeRange;
    activityApplicationPage(params).then((res) => {
      applyList.value = res.data?.records || [];
    });
  }
  function resetForm() {
    searchForm.value = { applicationName: '', className: '', activityId: '', timeRange: [] };
    getList();
  }
  function cancel(row) {
    ElMessageBox.confirm('确定取消该报名吗？', '提示',{
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(() => {
      cancelSignUpActivity(row.id, row.activityId).then(() => {
        ElMessage.success('已取消报名');
        getList();
      });
    });
  }
  onMounted(getList);
</script>
