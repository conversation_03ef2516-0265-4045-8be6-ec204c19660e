import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';

export const useAppStore = defineStore('app', () => {
  // Global loading state
  const isLoading = ref(false);
  const loadingText = ref('加载中...');

  // Dark mode state
  const isDarkMode = ref(false);
  function toggleDarkMode() {
    isDarkMode.value = !isDarkMode.value;
  }
  function setDarkMode(val) {
    isDarkMode.value = !!val;
  }

  // Cached data
  const cachedData = reactive({
    dashboardStats: null,
    classRankings: null,
    recentActivities: null,
    studentPointsOverview: null,
    pointsApplyStats: null,
  });

  // User info
  const userInfo = reactive({
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  });

  // Set loading state
  function setLoading(status, text = '加载中...') {
    isLoading.value = status;
    loadingText.value = text;
  }

  // Store cached data
  function setCachedData(key, data) {
    if (cachedData[key] !== undefined) {
      cachedData[key] = data;
    }
  }

  // Get cached data
  function getCachedData(key) {
    return cachedData[key];
  }

  // Check if data is cached
  function hasCachedData(key) {
    return cachedData[key] !== null;
  }

  // Clear all cached data
  function clearCache() {
    Object.keys(cachedData).forEach(key => {
      cachedData[key] = null;
    });
  }

  // Set user info
  function setUserInfo(info) {
    Object.assign(userInfo, info);
  }

  return {
    isLoading,
    loadingText,
    cachedData,
    userInfo,
    setLoading,
    setCachedData,
    getCachedData,
    hasCachedData,
    clearCache,
    setUserInfo,
    isDarkMode,
    toggleDarkMode,
    setDarkMode
  };
}); 