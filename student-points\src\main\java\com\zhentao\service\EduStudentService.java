package com.zhentao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.dto.BatchImportDto;
import com.zhentao.dto.BatchImportResultDto;
import com.zhentao.pojo.EduStudent;
import com.zhentao.utils.Result;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【edu_student】的数据库操作Service
* @createDate 2025-07-07 18:59:20
*/
public interface EduStudentService extends IService<EduStudent> {

    /**
     * 查询积分数在前十名的学生
     */
    public Result queryTopTenStudents();
    /**
     * 查询积分数在后十名的学生
     */
    public Result queryBottomTenStudents();
    /*
     * 批量导入学生
     * @param file Excel文件
     * @return 导入结果
     */
    Map<String, Object> batchImportStudents(MultipartFile file) throws Exception;

    /*
     * 下载学生信息导入模板
     * @param response HTTP响应
     */
    void downloadStudentTemplate(HttpServletResponse response) throws Exception;

    /*
     * 批量导入学生信息（从DTO）
     * @param importData 导入数据
     * @return 导入结果
     */
    BatchImportResultDto batchImportStudentsFromDto(BatchImportDto importData) throws Exception;

    /*
     * 验证导入数据
     * @param importData 导入数据
     * @return 验证结果
     */
    List<BatchImportDto.ClassImportDto> validateImportData(BatchImportDto importData) throws Exception;

    public List<EduStudent> searchStudents(String keyword);
}
