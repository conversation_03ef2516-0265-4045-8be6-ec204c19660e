import request from '@/request/index';

// 报名活动
export function signUpActivity(data) {
  return request.post('/activityApplication/signUpActivity', data);
}
// 取消报名活动
export function cancelSignUpActivity(applicationId, activityId) {
  return request.post('/activityApplication/cancelSignUpActivity', null, {
    params: { applicationId, activityId }
  });
}
// 活动报名列表
export function activityApplicationPage(data) {
  return request.post('/activityApplication/activityApplicationPage', data);
}

/**
 * 查询所有活动
 */
export function findAllActivity() {
  return request.post('/activityApplication/findActivity');
}
