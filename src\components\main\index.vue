<template>
  <div class="main-container">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 监听路由错误
const handleRouteError = (error) => {
  console.error('路由错误:', error);
};

onMounted(() => {
  router.onError(handleRouteError);
});

onUnmounted(() => {
  // 清理路由错误监听器
  router.off('error', handleRouteError);
});
</script>

<style scoped>
.main-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>