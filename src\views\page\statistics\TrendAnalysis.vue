<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 筛选表单
const filterForm = reactive({
  analysisType: 'student',
  targetId: '',
  dateRange: [],
  timeGranularity: 'month'
})

// 选项
const analysisTypeOptions = [
  { label: '学生趋势', value: 'student' },
  { label: '班级趋势', value: 'class' },
  { label: '整体趋势', value: 'overall' }
]

const timeGranularityOptions = [
  { label: '按日', value: 'day' },
  { label: '按周', value: 'week' },
  { label: '按月', value: 'month' },
  { label: '按季度', value: 'quarter' },
  { label: '按年', value: 'year' }
]

// 图表实例
const mainTrendChart = ref(null)
const cumulativeTrendChart = ref(null)
const weekdayDistributionChart = ref(null)
const categoryDistributionChart = ref(null)

// 图表对象
let mainChartInstance = null
let cumulativeChartInstance = null
let weekdayChartInstance = null
let categoryChartInstance = null

// 生命周期钩子
onMounted(() => {
  loadData()
  // 使用setTimeout确保DOM元素已经渲染
  setTimeout(() => {
    initCharts()
  }, 300)
})

// 组件卸载时清理图表实例和事件监听器
onUnmounted(() => {
  if (mainChartInstance) {
    mainChartInstance.dispose()
  }
  if (cumulativeChartInstance) {
    cumulativeChartInstance.dispose()
  }
  if (weekdayChartInstance) {
    weekdayChartInstance.dispose()
  }
  if (categoryChartInstance) {
    categoryChartInstance.dispose()
  }
  // 移除窗口resize事件监听
  window.removeEventListener('resize', handleResize)
})

// 统一的resize处理函数
const handleResize = () => {
  if (mainChartInstance) {
    mainChartInstance.resize()
  }
  if (cumulativeChartInstance) {
    cumulativeChartInstance.resize()
  }
  if (weekdayChartInstance) {
    weekdayChartInstance.resize()
  }
  if (categoryChartInstance) {
    categoryChartInstance.resize()
  }
}

// 添加resize事件监听
window.addEventListener('resize', handleResize)

const handleSearch = () => {
  loadData()
  refreshCharts()
}

const resetFilter = () => {
  filterForm.analysisType = 'student'
  filterForm.targetId = ''
  filterForm.dateRange = []
  filterForm.timeGranularity = 'month'
  
  loadData()
  refreshCharts()
}

const loadData = () => {
  // 在实际应用中，这里应该根据筛选条件从后端加载数据
}

const refreshData = () => {
  loadData()
  refreshCharts()
}

// 分析类型变化处理
const handleAnalysisTypeChange = () => {
  filterForm.targetId = ''
  refreshCharts()
}

const initCharts = () => {
  // 初始化主趋势图表
  if (mainTrendChart.value) {
    // 如果已存在实例，先销毁
    if (mainChartInstance) {
      mainChartInstance.dispose()
    }
    
    mainChartInstance = echarts.init(mainTrendChart.value)
    const option = {
      title: {
        text: '积分趋势分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['增加积分', '扣除积分', '净积分'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value',
        name: '积分'
      },
      series: [
        {
          name: '增加积分',
          type: 'line',
          stack: 'Total',
          areaStyle: {},
          emphasis: {
            focus: 'series'
          },
          data: [10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65],
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(103, 194, 58, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(103, 194, 58, 0.1)'
              }
            ])
          }
        },
        {
          name: '扣除积分',
          type: 'line',
          stack: 'Total',
          areaStyle: {},
          emphasis: {
            focus: 'series'
          },
          data: [-5, -8, -10, -12, -15, -18, -20, -22, -25, -28, -30, -32],
          itemStyle: {
            color: '#F56C6C'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(245, 108, 108, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(245, 108, 108, 0.1)'
              }
            ])
          }
        },
        {
          name: '净积分',
          type: 'line',
          data: [5, 7, 10, 13, 15, 17, 20, 23, 25, 27, 30, 33],
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    }
    mainChartInstance.setOption(option)
  }

  // 初始化累积趋势图表
  if (cumulativeTrendChart.value) {
    // 如果已存在实例，先销毁
    if (cumulativeChartInstance) {
      cumulativeChartInstance.dispose()
    }
    
    cumulativeChartInstance = echarts.init(cumulativeTrendChart.value)
    const option = {
      title: {
        text: '累积积分趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value',
        name: '累积积分'
      },
      series: [
        {
          name: '累积积分',
          type: 'line',
          data: [5, 12, 22, 35, 50, 67, 87, 110, 135, 162, 192, 225],
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均值' }
            ]
          },
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#409EFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.4)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ])
          }
        }
      ]
    }
    cumulativeChartInstance.setOption(option)
  }

  // 初始化星期分布图表
  if (weekdayDistributionChart.value) {
    // 如果已存在实例，先销毁
    if (weekdayChartInstance) {
      weekdayChartInstance.dispose()
    }
    
    weekdayChartInstance = echarts.init(weekdayDistributionChart.value)
    const option = {
      title: {
        text: '星期积分分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['增加积分', '扣除积分'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value',
        name: '积分'
      },
      series: [
        {
          name: '增加积分',
          type: 'bar',
          data: [10, 12, 15, 18, 20, 8, 5],
          itemStyle: {
            color: '#67C23A'
          }
        },
        {
          name: '扣除积分',
          type: 'bar',
          data: [-5, -6, -7, -8, -9, -3, -2],
          itemStyle: {
            color: '#F56C6C'
          }
        }
      ]
    }
    weekdayChartInstance.setOption(option)
  }

  // 初始化类别分布图表
  if (categoryDistributionChart.value) {
    // 如果已存在实例，先销毁
    if (categoryChartInstance) {
      categoryChartInstance.dispose()
    }
    
    categoryChartInstance = echarts.init(categoryDistributionChart.value)
    const option = {
      title: {
        text: '积分类别分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '积分类别',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 40, name: '学习成绩' },
            { value: 20, name: '课外活动' },
            { value: 15, name: '志愿服务' },
            { value: 10, name: '班级贡献' },
            { value: 15, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    categoryChartInstance.setOption(option)
  }
}

const refreshCharts = () => {
  // 在实际应用中，这里应该根据筛选条件重新加载图表数据
  setTimeout(() => {
    initCharts()
  }, 300)
}
</script>

<template>
  <div class="trend-analysis-container">
    <div class="page-header">
      <h2>积分趋势分析</h2>
      <el-button type="primary" :icon="Refresh" circle @click="refreshData" />
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="分析类型">
          <el-select v-model="filterForm.analysisType" placeholder="请选择分析类型" clearable>
            <el-option
              v-for="item in analysisTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标">
          <el-select v-model="filterForm.targetId" placeholder="请选择目标" clearable>
            <el-option
              v-for="item in classOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="时间粒度">
          <el-select v-model="filterForm.timeGranularity" placeholder="请选择时间粒度" clearable>
            <el-option
              v-for="item in timeGranularityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 概览卡片 -->
    <div class="overview-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">总积分</div>
            <div class="overview-value">{{ statsData.totalPoints }}</div>
            <div class="overview-footer">累计总积分</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">月均增长</div>
            <div class="overview-value">{{ statsData.avgMonthlyGrowth }}</div>
            <div class="overview-footer">积分/月</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">最高月份</div>
            <div class="overview-value">{{ statsData.highestMonth }}</div>
            <div class="overview-footer">积分增长最高</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">增长率</div>
            <div class="overview-value">{{ statsData.growthRate }}%</div>
            <div class="overview-footer">较上年</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <div ref="mainTrendChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div ref="cumulativeTrendChart" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div ref="weekdayDistributionChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <div ref="categoryDistributionChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 分析结论 -->
    <div class="conclusion-container">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>分析结论</span>
          </div>
        </template>
        <div class="conclusion-content">
          <h3>总体趋势</h3>
          <p>根据数据分析，学生积分整体呈现稳步上升的趋势，年增长率达到{{ statsData.growthRate }}%。其中{{ statsData.highestMonth }}的增长最为显著，而{{ statsData.lowestMonth }}的增长较为缓慢。</p>
          
          <h3>类型分布</h3>
          <p>从积分类型分布来看，学习成绩类积分占比最高，达到40%；其次是课外活动类积分，占比25%；志愿服务类积分占比15%；班级贡献类积分占比10%；其他类型积分占比10%。</p>
          
          <h3>班级对比</h3>
          <p>不同班级之间的积分差异明显，信息安全1班的平均积分最高，人工智能1班的平均积分最低。但从增长率来看，人工智能1班的增长速度最快，表明其正在迎头赶上。</p>
          
          <h3>建议</h3>
          <p>建议加强对积分增长较慢班级的指导和支持，同时可以考虑在{{ statsData.lowestMonth }}增加一些积分活动，以提高该月份的积分增长。</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.trend-analysis-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.trend-analysis-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.filter-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.overview-container {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.overview-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.overview-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.overview-footer {
  font-size: 12px;
  color: #909399;
}

.charts-container {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
  height: 400px;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.chart-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.chart {
  height: 100%;
  width: 100%;
}

.chart-row {
  margin-top: 20px;
}

.conclusion-container {
  margin-bottom: 20px;
}

.conclusion-container .el-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.conclusion-container .el-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conclusion-content {
  line-height: 1.6;
}

.conclusion-content h3 {
  font-size: 16px;
  margin-top: 15px;
  margin-bottom: 10px;
  color: #303133;
}

.conclusion-content p {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (max-width: 768px) {
  .filter-container {
    padding: 15px;
  }

  .chart-card {
    height: 300px;
  }
}
</style>