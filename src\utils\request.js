import axios from 'axios';
import { ElMessage } from 'element-plus';
import router from '@/router';
import { getToken } from '@/utils/userUtils';

// 创建axios实例
const service = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 获取token
    const token = getToken();
    if (token) {
      config.headers.Authorization = token;
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }
    
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 检查是否有新的token需要更新
    const newToken = response.headers['new-token'];
    if (newToken) {
      localStorage.setItem('Authorization', newToken);
    }
    
    // 处理特殊情况：字符串响应
    if (typeof response.data === 'string') {
      if (response.data.includes('成功')) {
        return {
          data: {
            code: 200,
            message: response.data,
            data: null
          }
        };
      } else {
        return {
          data: {
            code: 500,
            message: response.data,
            data: null
          }
        };
      }
    }
    
    // 检查响应状态码
    if (response.data && response.data.code !== undefined) {
      if (response.data.code === 200) {
        // 成功响应
        return response;
      } else if (response.data.code === 401) {
        // 未授权
        localStorage.removeItem('Authorization');
        ElMessage.error(response.data.message || '登录已过期，请重新登录');
        router.push('/login');
        return Promise.reject(new Error(response.data.message || '未授权'));
      } else {
        // 其他业务错误
        ElMessage.error(response.data.message || '操作失败');
        return Promise.reject(new Error(response.data.message || '操作失败'));
      }
    }
    
    return response;
  },
  (error) => {
    console.error('响应错误:', error);
    
    // 处理响应错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('Authorization');
          ElMessage.error('登录已过期，请重新登录');
          router.push('/login');
          break;
        case 403:
          // 权限不足
          ElMessage.error('权限不足，无法访问');
          break;
        case 404:
          // 资源不存在
          ElMessage.error('请求的资源不存在');
          break;
        case 400:
          // 请求错误
          ElMessage.error(error.response.data?.message || '请求参数错误，请检查输入');
          break;
        default:
          // 其他错误
          ElMessage.error(error.response.data?.message || '服务器错误');
          break;
      }
    } else if (error.request) {
      // 请求已发出但未收到响应
      ElMessage.error('网络连接失败，请检查网络');
    } else {
      // 请求配置错误
      ElMessage.error('请求错误: ' + error.message);
    }
    
    return Promise.reject(error);
  }
);

// 为了与原始request/index.js兼容
export default service; 