<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getClassPointsOverview, getClassPointsTrend, getClassPointsDistribution, getClassPointsDetails } from '@/api/system/statistics'
import { getClassStatistics } from '@/api/system/class'

// 筛选表单
const filterForm = reactive({
  className: '',
  dateRange: []
})

// 班级选项
const classOptions = ref([])

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 概览数据
const overviewData = reactive({
  totalClasses: 0,
  avgPoints: 0,
  highestPoints: 0,
  highestClassName: '',
  lowestPoints: 0,
  lowestClassName: ''
})

// 图表实例
const classTrendChart = ref(null)
const classComparisonChart = ref(null)
const pointsDistributionChart = ref(null)

// 图表对象
let trendChartInstance = null
let comparisonChartInstance = null
let distributionChartInstance = null

// 图表数据
const trendData = reactive({
  months: [],
  series: []
})

// 班级详情弹窗相关
const detailDialogVisible = ref(false)
const detailLoading = ref(false)
const detailData = ref({})

// 生命周期钩子
let timer = null;
onMounted(() => {
  fetchOverview()
  loadData()
  fetchTrendData()
  // 使用setTimeout确保DOM元素已经渲染
  setTimeout(() => {
    initCharts()
  }, 300)
  // 每30秒自动刷新一次数据
  timer = setInterval(() => {
    refreshData()
  }, 30000)
})

// 组件卸载时清理图表实例和事件监听器
onUnmounted(() => {
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  if (comparisonChartInstance) {
    comparisonChartInstance.dispose()
  }
  if (distributionChartInstance) {
    distributionChartInstance.dispose()
  }
  // 移除窗口resize事件监听
  window.removeEventListener('resize', handleResize)
  // 清理定时器
  if (timer) clearInterval(timer)
})

// 统一的resize处理函数
const handleResize = () => {
  if (trendChartInstance) {
    trendChartInstance.resize()
  }
  if (comparisonChartInstance) {
    comparisonChartInstance.resize()
  }
  if (distributionChartInstance) {
    distributionChartInstance.resize()
  }
}

// 添加resize事件监听
window.addEventListener('resize', handleResize)

// 方法定义
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'dateRange') {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  currentPage.value = 1
  loadData()
}

// 获取概览数据
const fetchOverview = async () => {
  try {
    const res = await getClassPointsOverview()
    if (res.data && res.data.code === 200) {
      const data = res.data.data || {}
      overviewData.totalClasses = data.totalClasses || 0
      overviewData.avgPoints = data.avgPoints || 0
      overviewData.highestPoints = data.highestPoints || 0
      overviewData.highestClassName = data.highestClassName || ''
      overviewData.lowestPoints = data.lowestPoints || 0
      overviewData.lowestClassName = data.lowestClassName || ''
    } else {
      console.warn('获取班级概览数据失败:', res)
      ElMessage.warning('获取班级概览数据失败')
    }
  } catch (error) {
    console.error('获取班级概览数据出错:', error)
    ElMessage.error('获取班级概览数据出错')
  }
}

// 获取班级排名数据
const loadData = async () => {
  loading.value = true
  try {
    // 获取班级详情数据
    const params = {
      className: filterForm.className
    }
    const res = await getClassPointsDetails(params)
    if (res.data && res.data.code === 200) {
      const data = res.data.data || []
      tableData.value = data
      total.value = data.length
      
      // 更新班级选项
      classOptions.value = data.map(item => item.className)
    } else {
      console.warn('获取班级详情数据失败:', res)
      ElMessage.warning('获取班级详情数据失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取班级详情数据出错:', error)
    ElMessage.error('获取班级详情数据出错')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取趋势数据
const fetchTrendData = async () => {
  try {
    const res = await getClassPointsTrend()
    if (res.data && res.data.code === 200) {
      const data = res.data.data || {}
      trendData.months = data.months || []
      trendData.series = data.series || []
      
      // 更新趋势图
      updateTrendChart()
    } else {
      console.warn('获取班级趋势数据失败:', res)
    }
  } catch (error) {
    console.error('获取班级趋势数据出错:', error)
  }
}

// 获取分布数据
const fetchDistributionData = async () => {
  try {
    const res = await getClassPointsDistribution()
    if (res.data && res.data.code === 200) {
      const data = res.data.data || []
      
      // 更新分布图
      updateDistributionChart(data)
    } else {
      console.warn('获取班级分布数据失败:', res)
    }
  } catch (error) {
    console.error('获取班级分布数据出错:', error)
  }
}

const refreshData = () => {
  fetchOverview()
  loadData()
  fetchTrendData()
  fetchDistributionData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

const initCharts = () => {
  // 初始化班级积分趋势图
  if (classTrendChart.value) {
    // 如果已存在实例，先销毁
    if (trendChartInstance) {
      trendChartInstance.dispose()
    }
    
    trendChartInstance = echarts.init(classTrendChart.value)
    updateTrendChart()
  }

  // 初始化班级对比图
  if (classComparisonChart.value) {
    // 如果已存在实例，先销毁
    if (comparisonChartInstance) {
      comparisonChartInstance.dispose()
    }
    
    comparisonChartInstance = echarts.init(classComparisonChart.value)
    updateComparisonChart()
  }

  // 初始化积分分布图
  if (pointsDistributionChart.value) {
    // 如果已存在实例，先销毁
    if (distributionChartInstance) {
      distributionChartInstance.dispose()
    }
    
    distributionChartInstance = echarts.init(pointsDistributionChart.value)
    fetchDistributionData()
  }
}

// 更新趋势图
const updateTrendChart = () => {
  if (!trendChartInstance) return
  
  const option = {
    title: {
      text: '班级积分趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: trendData.series.map(item => item.name),
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendData.months
    },
    yAxis: {
      type: 'value',
      name: '平均积分'
    },
    series: trendData.series.map(item => ({
      name: item.name,
      type: 'line',
      data: item.data,
      smooth: true
    }))
  }
  
  trendChartInstance.setOption(option)
}

// 更新对比图
const updateComparisonChart = () => {
  if (!comparisonChartInstance || !tableData.value.length) return
  
  // 获取前10个班级
  const top10Classes = tableData.value.slice(0, 10)
  
  const option = {
    title: {
      text: '班级积分对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '平均积分'
    },
    yAxis: {
      type: 'category',
      data: top10Classes.map(item => item.className),
      axisLabel: {
        interval: 0,
        rotate: 0
      }
    },
    series: [
      {
        name: '平均积分',
        type: 'bar',
        data: top10Classes.map(item => item.avgPoints),
        label: {
          show: true,
          position: 'right'
        },
        itemStyle: {
          color: function(params) {
            const colorList = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#6B778C', '#B983FF'];
            return colorList[params.dataIndex % colorList.length];
          }
        }
      }
    ]
  }
  
  comparisonChartInstance.setOption(option)
}

// 更新分布图
const updateDistributionChart = (data) => {
  if (!distributionChartInstance) return
  
  const option = {
    title: {
      text: '班级积分分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '积分区间',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }
  
  distributionChartInstance.setOption(option)
}

// 获取分页数据
const pagedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

const handleViewDetail = async (row) => {
  detailDialogVisible.value = true
  detailLoading.value = true
  try {
    const res = await getClassStatistics(row.id)
    if (res.data && res.data.code === 200) {
      detailData.value = res.data.data || {}
    } else {
      ElMessage.error('获取班级详情失败')
      detailData.value = {}
    }
  } catch (e) {
    ElMessage.error('获取班级详情出错')
    detailData.value = {}
  } finally {
    detailLoading.value = false
  }
}
</script>

<template>
  <div class="class-statistics-container">
    <div class="page-header">
      <h2>班级积分分析</h2>
      <el-button type="primary" :icon="Refresh" circle @click="refreshData" />
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="班级">
          <el-select v-model="filterForm.className" placeholder="请选择班级" clearable>
            <el-option
              v-for="item in classOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 概览卡片 -->
    <div class="overview-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">总班级数</div>
            <div class="overview-value">{{ overviewData.totalClasses }}</div>
            <div class="overview-footer">全校班级总数</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">平均积分</div>
            <div class="overview-value">{{ overviewData.avgPoints }}</div>
            <div class="overview-footer">全校班级平均</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">最高班级</div>
            <div class="overview-value">{{ overviewData.highestPoints }}</div>
            <div class="overview-footer">{{ overviewData.highestClassName }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">最低班级</div>
            <div class="overview-value">{{ overviewData.lowestPoints }}</div>
            <div class="overview-footer">{{ overviewData.lowestClassName }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <div ref="classTrendChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <!-- 移除积分分布图表区域（环形图） -->
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>班级积分详情</span>
          </div>
        </template>
        <el-table
          v-loading="loading"
          :data="pagedData"
          border
          style="width: 100%"
        >
          <el-table-column prop="className" label="班级名称" min-width="180" />
          <el-table-column prop="totalStudents" label="学生人数" width="100" />
          <el-table-column prop="avgPoints" label="平均积分" width="100" sortable>
            <template #default="scope">
              <span class="avg-points">{{ scope.row.avgPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="highestPoints" label="最高积分" width="100">
            <template #default="scope">
              <span class="positive">{{ scope.row.highestPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="lowestPoints" label="最低积分" width="100">
            <template #default="scope">
              <span class="negative">{{ scope.row.lowestPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="passRate" label="及格率" width="100">
            <template #default="scope">
              <span>{{ scope.row.passRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="rank" label="当前排名" width="100" sortable />
          <el-table-column label="排名变化" width="120">
            <template #default="scope">
              <span :class="scope.row.lastMonthRank > scope.row.rank ? 'up' : (scope.row.lastMonthRank < scope.row.rank ? 'down' : '')">
                {{ scope.row.lastMonthRank > scope.row.rank ? '↑' : (scope.row.lastMonthRank < scope.row.rank ? '↓' : '-') }}
                {{ Math.abs(scope.row.lastMonthRank - scope.row.rank) || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleViewDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:currentPage="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    <!-- 班级详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="班级详情" width="400px" :close-on-click-modal="false">
      <el-descriptions v-if="!detailLoading" :column="1" border>
        <el-descriptions-item label="班级名称">{{ detailData.className }}</el-descriptions-item>
        <el-descriptions-item label="学生人数">{{ detailData.totalStudents }}</el-descriptions-item>
        <el-descriptions-item label="男生人数">{{ detailData.maleCount }}</el-descriptions-item>
        <el-descriptions-item label="女生人数">{{ detailData.femaleCount }}</el-descriptions-item>
        <el-descriptions-item label="平均积分">{{ detailData.averagePoints }}</el-descriptions-item>
        <el-descriptions-item label="最高积分">{{ detailData.maxPoints }}</el-descriptions-item>
        <el-descriptions-item label="最低积分">{{ detailData.minPoints }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ detailData.stageName }}</el-descriptions-item>
      </el-descriptions>
      <div v-else style="text-align:center;padding:40px 0;">
        <el-icon><Refresh /></el-icon> 加载中...
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.class-statistics-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.class-statistics-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.filter-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.overview-container {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.overview-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.overview-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.overview-footer {
  font-size: 12px;
  color: #909399;
}

.charts-container {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.chart-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.chart {
  height: 400px;
  width: 100%;
}

.chart-row {
  margin-top: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.table-container .el-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.table-container .el-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.avg-points {
  font-weight: bold;
  color: #409EFF;
}

.positive {
  color: #67c23a;
  font-weight: bold;
}

.negative {
  color: #f56c6c;
  font-weight: bold;
}

.up {
  color: #67c23a;
}

.down {
  color: #f56c6c;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>