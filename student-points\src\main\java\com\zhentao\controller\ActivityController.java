package com.zhentao.controller;

import com.zhentao.config.MinioConfig;
import com.zhentao.dto.system.system.ActivityDto;
import com.zhentao.service.ActivityService;
import com.zhentao.utils.Result;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

/**
 * <p>
 * 活动表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@RestController
@RequestMapping("/activity")
public class ActivityController {
    @Autowired
    private ActivityService activityService;
    @Autowired
    private MinioConfig minioConfig;
    @Autowired
    private MinioClient minioClient;


    /**
     * 添加活动
     * @param activityDto
     * @return
     */
    @PostMapping("/addActivity")
    public Result addActivity(@RequestBody ActivityDto activityDto){
        return activityService.addActivity(activityDto);
    }
    /**
     * 删除活动
     * @param activityId
     * @return
     */
    @PostMapping("/deleteActivity")
    public Result deleteActivity(Integer activityId){
        return activityService.deleteActivity(activityId);
    }
    /**
     * 修改活动
     * @param activityDto
     * @return
     */
    @PostMapping("/updateActivity")
    public Result updateActivity(@RequestBody ActivityDto activityDto){
        return activityService.updateActivity(activityDto);
    }
    /**
     * 查询活动
     * @return
     */
    @PostMapping("/findActivity")
    public Result findActivity(){
        return activityService.findActivity();
    }
    /**
     * 修改活动状态
     * @param activityId
     * @param status
     * @return
     */
    @PostMapping("/changeActivityStatus")
    public Result changeActivityStatus(Integer activityId,Integer status){
        return activityService.changeActivityStatus(activityId,status);
    }
    @PostMapping("/uploadFile")
    public Result uploadFile(@RequestParam("file") MultipartFile file) {

        try {
            // 1. 验证 MinIO 连接


            // 2. 检查存储桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucket())
                    .build());
            if (!isExist) {
                return Result.ERROR("存储桶不存在");
            }
            //1.检查文件是否为空
            if (file.isEmpty()){
                return Result.ERROR("上传图片不能为空");
            }
            // 2. 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID().toString() + extension;

            // 3. 上传文件到minio
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucket())
                            .object(fileName)
                            .contentType(file.getContentType())
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .build()
            );


            // 4. 获取文件访问URL
            String fileUrl = minioClient.getObjectUrl(minioConfig.getBucket(), fileName);
            String cleanUrl = fileUrl.split("\\?")[0];
            System.out.println("文件访问地址: " + cleanUrl);

            // 6. 保存到数据库

            return Result.OK(cleanUrl);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("上传失败: " + e.getMessage());
        }
    }
}
