<template>
  <div class="common-layout">
    <el-container>
      <el-aside :width="isSidebarCollapsed ? '64px' : '240px'" class="aside-container">
        <Aside />
      </el-aside>
      <el-container>
        <el-header height="60px">
          <Header />
        </el-header>
        <el-main>
          <Main />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import Aside from '../components/aside/index.vue'
import Header from '../components/header/index.vue'
import Main from '../components/main/index.vue'

const isSidebarCollapsed = ref(false)
provide('isSidebarCollapsed', isSidebarCollapsed)
</script>

<style scoped>
.common-layout {
  height: 100vh;
  overflow: hidden;
}

.aside-container {
  transition: width 0.3s;
  background-color: #304156;
  overflow: hidden;
  height: 100vh;
  position: relative;
}

:deep(.el-header) {
  padding: 0;
}

:deep(.el-main) {
  padding: 20px;
  background-color: #f0f2f5;
  overflow-y: auto;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-main::-webkit-scrollbar) {
  display: none;
}

:deep(.el-main > div) {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}
</style>
