package com.zhentao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName: MpGenerator
 * @Author: 振涛教育_李小超
 * @Date: 2024年3月8日 08:37
 */
public class MpGenerator {

    public static void main(String[] args) {
        // 使用元数据查询的方式生成代码,默认已经根据jdbcType来适配java类型,支持使用typeConvertHandler来转换需要映射的类型映射
        DataSourceConfig.Builder root = new DataSourceConfig.Builder("**************************************************************************************", "feidaowudi", "feidaowudi_520")
                .schema("mybatis-plus")
                .keyWordsHandler(new MySqlKeyWordsHandler());

        FastAutoGenerator.create(root)
                // 全局配置
                .globalConfig((scanner, builder) -> {
                    builder.outputDir(System.getProperty("user.dir")+"/src/main/java").author("mp")
                            .fileOverride();
                })

                // 包配置
                .packageConfig((scanner, builder) ->
                        builder.entity("pojo")
                                .controller("controller")
                                .service("service")
                                .mapper("mapper")
                                .xml("mapper")
                                .parent(scanner.apply("请输入包名？"))
                )
                // 策略配置
                .strategyConfig((scanner, builder) -> builder.addTablePrefix("t_").addInclude(getTables(scanner.apply("请输入表名，多个英文逗号分隔？所有输入 all")))
                        .controllerBuilder().enableRestStyle().enableHyphenStyle()
                        .entityBuilder().enableLombok().addTableFills(
                                new Column("create_time", FieldFill.INSERT)
                        ).build())

                /*
                    模板引擎配置，默认 Velocity 可选模板引擎 Beetl 或 Freemarker
                   .templateEngine(new BeetlTemplateEngine())
                   .templateEngine(new FreemarkerTemplateEngine())
                 */
                .execute();
    }

    // 处理 all 情况
    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }

}
