import Request from "@/request/index.js";

/**
 * 查询所有角色
 * @returns {*}
 */
export function findAllRole(){
    return Request({
        url: '/sysRole/findAllRole',
        method: 'post'
    })
}

/**
 * 查询角色详情
 * @param roleId
 * @returns {*}
 */
export function findRoleById(roleId){
    return Request({
        url: '/sysRole/findRoleById',
        method: 'post',
        params: {
            roleId
        }
    })
}

/**
 * 查询所有权限
 * @returns {*}
 */
export function findAllPermission(){
    return Request({
        url: '/sysRole/findAllPermission',
        method: 'post',
    })
}

/**
 * 查询角色权限
 * @param roleId
 * @returns {*}
 */
export function findRolePermission(roleId){
    return Request({
        url: '/sysRole/findRolePermission',
        method: 'post',
        data:{
            roleId
        }
    })
}

/**
 * 添加角色
 * @param data
 * @returns {*}
 */
export function addRole(data){
    return Request({
        url: '/sysRole/addRole',
        method: 'post',
        data
    })
}

/**
 * 更新角色
 * @param data
 * @returns {*}
 */
export function updateRole(data){
    return Request({
        url: '/sysRole/updateRole',
        method: 'post',
        data
    })
}

/**
 * 删除角色
 * @param roleId
 * @returns {*}
 */
export function deleteRole(roleId){
    return Request({
        url: '/sysRole/deleteRole',
        method: 'post',
        params: {
            roleId
        }
    })
}

/**
 * 更新角色权限
 * @param roleId
 * @param menuIds
 * @returns {*}
 */
export function updateRolePermission(roleId, menuIds){
    return Request({
        url: '/sysRole/updateRolePermission',
        method: 'post',
        data: {
            roleId,
            menuIds
        }
    })
}

/**
 * 获取角色关联的用户列表
 * @param roleId
 * @returns {*}
 */
export function findRoleUsers(roleId){
    return Request({
        url: '/sysRole/findRoleUsers',
        method: 'post',
        params: {
            roleId
        }
    })
}

/**
 * 根据ID查询用户详情
 * @param userId
 * @returns {*}
 */
export function findUserById(userId){
    return Request({
        url: '/sysUser/findUserById',
        method: 'post',
        params: {
            userId
        }
    })
}

/**
 * 更新用户角色
 * @param data
 * @returns {*}
 */
export function updateUserRole(data){
    return Request({
        url: '/sysUser/updateUserRole',
        method: 'post',
        data
    })
}

