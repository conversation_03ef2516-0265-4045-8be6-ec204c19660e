package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.dto.system.system.ApplicationDto;
import com.zhentao.dto.system.system.PageActivityApplicationDto;
import com.zhentao.mapper.ActivityApplicationMapper;
import com.zhentao.mapper.ActivityMapper;
import com.zhentao.mapper.EduClassMapper;
import com.zhentao.mapper.EduStudentMapper;
import com.zhentao.pojo.Activity;
import com.zhentao.pojo.ActivityApplication;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.service.ActivityApplicationService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Service
public class ActivityApplicationServiceImpl extends ServiceImpl<ActivityApplicationMapper, ActivityApplication> implements ActivityApplicationService {
    @Autowired
    private ActivityApplicationMapper activityApplicationMapper;
    @Autowired
    private ActivityMapper activityMapper;
    @Autowired
    private EduStudentMapper eduStudentMapper;
    @Autowired
    private EduClassMapper eduClassMapper;
    @Override
    public Result signUpActivity(ApplicationDto applicationDto) {
        if (applicationDto!=null){
            QueryWrapper<ActivityApplication> activityApplicationQueryWrapper=new QueryWrapper<>();
            activityApplicationQueryWrapper.eq("activity_id",applicationDto.getActivityId());
            activityApplicationQueryWrapper.eq("name",applicationDto.getName());
            ActivityApplication activityApplication1 = activityApplicationMapper.selectOne(activityApplicationQueryWrapper);
            QueryWrapper<EduStudent> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("real_name",applicationDto.getName());
            EduStudent eduStudent = eduStudentMapper.selectOne(queryWrapper);
            if (eduStudent==null){
                return Result.ERROR("该学生不存在该学院，请填写真实姓名");
            }
            Integer classId = eduStudent.getClassId();
            EduClass eduClass = eduClassMapper.selectById(classId);
            if (eduClass==null){
                return Result.ERROR("该班级不存在");
            }
            if (!eduClass.getClassName().equals(applicationDto.getClassName())){
                return Result.ERROR("该学生不属于该班级");
            }
            if (activityApplication1!=null){
                return Result.ERROR("该学生已报名该活动,请不要重复报名");
            }
            ActivityApplication activityApplication = new ActivityApplication();
            activityApplication.setName(applicationDto.getName());
            activityApplication.setPhone(applicationDto.getPhone());
            activityApplication.setClassName(applicationDto.getClassName());
            activityApplication.setActivityId(applicationDto.getActivityId());
            activityApplication.setCreateTime(new Date());
            int insert = activityApplicationMapper.insert(activityApplication);
            if (insert>0){
                Activity activity = activityMapper.selectById(applicationDto.getActivityId());
                if (activity.getNumber()==0){
                    return Result.ERROR("人员已满");
                }
                activity.setNumber(activity.getNumber()-1);
                activityMapper.updateById(activity);
                return Result.OK("报名成功");
            }else {
                return Result.ERROR("报名失败");
            }
        }else {
            return Result.ERROR("数据不对");
        }

    }

    @Override
    public Result cancelSignUpActivity(Integer applicationId, Integer activityId) {
        QueryWrapper<ActivityApplication> activityApplicationQueryWrapper=new QueryWrapper<>();
        activityApplicationQueryWrapper.eq("id",applicationId);
        activityApplicationQueryWrapper.eq("activity_id",activityId);
        ActivityApplication activityApplication = activityApplicationMapper.selectOne(activityApplicationQueryWrapper);
        if (activityApplication != null) {
            int delete = activityApplicationMapper.delete(activityApplicationQueryWrapper);
            if (delete>0){
                return Result.OK("取消报名成功");
            }else {
                return Result.ERROR("取消报名失败");
            }
        }else {
            return Result.ERROR("取消报名失败");
        }
    }

    @Override
    public Page<ActivityApplication> activityApplicationPage(PageActivityApplicationDto pageActivityApplicationDto) {
        Page<ActivityApplication> page=new Page<>(pageActivityApplicationDto.getPageCurrent(),pageActivityApplicationDto.getPageSize());
        QueryWrapper<ActivityApplication> activityApplicationQueryWrapper=new QueryWrapper<>();
        activityApplicationQueryWrapper.eq(pageActivityApplicationDto.getActivityId()!=null,"activity_id",pageActivityApplicationDto.getActivityId());
        activityApplicationQueryWrapper.like(pageActivityApplicationDto.getApplicationName()!=null,"name",pageActivityApplicationDto.getApplicationName());
        activityApplicationQueryWrapper.like(pageActivityApplicationDto.getClassName()!=null,"class_name",pageActivityApplicationDto.getClassName());
        if (pageActivityApplicationDto.getTimeRange()!=null) {
            Date[] timeRange = pageActivityApplicationDto.getTimeRange();
            if (timeRange.length >= 2 && timeRange[0] != null && timeRange[1] != null) {
                activityApplicationQueryWrapper.between("create_time", timeRange[0], timeRange[1]);
            }
        }
        activityApplicationQueryWrapper.orderByDesc("create_time");
        Page<ActivityApplication> activityApplicationPage = activityApplicationMapper.selectPage(page, activityApplicationQueryWrapper);
        List<ActivityApplication> records = activityApplicationPage.getRecords();

        for (ActivityApplication activityApplication : records) {
            Integer activityId = activityApplication.getActivityId();
            QueryWrapper<Activity> queryWrapper1=new QueryWrapper<>();
            queryWrapper1.eq("id",activityId);
            Activity activity = activityMapper.selectOne(queryWrapper1);
            activityApplication.setActivityName(activity.getActivityName());
        }
        return activityApplicationPage;
    }

    @Override
    public Result findActivity() {
        QueryWrapper<Activity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",1);
        List<Activity> activities = activityMapper.selectList(queryWrapper);
        if (activities!=null && !activities.isEmpty()){
            return Result.OK(activities);
        }else {
            return Result.ERROR("没有活动");
        }
    }

}
