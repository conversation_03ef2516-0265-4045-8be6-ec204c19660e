package com.zhentao.config;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.file.AuthFile;
import com.zhentao.mapper.SysMenuMapper;
import com.zhentao.mapper.SysUserMapper;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.pojo.SysMenu;
import com.zhentao.pojo.SysUser;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.OperationLogService;
import com.zhentao.utils.JwtService;
import com.zhentao.utils.Result;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.intercept.RequestAuthorizationContext;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Autowired
    private AuthFile authFile;
    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(conf -> conf.disable());
        http.authorizeHttpRequests(conf -> conf.requestMatchers(
                                "/user/login",
                                "/user/logout",
                                "/error", "/edu-student/getClassOptions",
                                "/points-record/findAll", "/activity/findActivity",
                                "/activityApplication/signUpActivity", "/edu-student/template/download",
                                "/edu-student/batch/import", "/edu-student/batch/validate", "/edu-student/queryTopTenStudents",
                                "/edu-student/queryBottomTenStudents", "/edu-class/queryTopThreeClass", "/points-apply/queryTodayAddPoints",
                                "/points-apply/queryTodayMinusPoints"

                        )
                        .permitAll()
                        .anyRequest()
                        .access(this::check)
        );

        http.formLogin(conf -> conf.loginProcessingUrl("/user/login")
                .successHandler(this::onAuthenticationSuccess)
                .failureHandler(this::onAuthenticationFailure)
        );

        http.exceptionHandling(conf -> conf
                .accessDeniedHandler(this::handle)
                .authenticationEntryPoint(this::commence)
        );

        http.sessionManagement(conf -> conf
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
        );

        http.logout(conf -> conf.logoutUrl("/user/logout")
                .logoutSuccessHandler(this::onLogoutSuccess)
        );

        http.addFilterBefore(authFile, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private OperationLogService operationLogService;

    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        SysUser user = (SysUser) authentication.getPrincipal();
        String username = user.getUsername();
        String realName = user.getRealName();
        Integer userId = user.getUserId();
        String avatar = user.getAvatar();
        List<String> perms = user.getPerms();
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser.getStatus()==1){
            // 用户被禁用，记录登录失败日志
            operationLogService.recordLog(username, realName, 1, "system",
                    "用户登录失败：账号已被禁用或密码错误", 2, request);

            Result result = Result.ERROR("该用户已被禁用，请联系管理员");
            response.setContentType("application/json;charset=utf-8");
            PrintWriter writer = response.getWriter();
            writer.write(JSON.toJSONString(result));
        }else {
            // 登录成功，记录登录成功日志
            operationLogService.recordLog(username, realName, 1, "system",
                    "用户登录成功", 1, request);
            Map<String, Object>map=new HashMap<>();
            QueryWrapper<SysUserRole> sysUserRoleQueryWrapper=new QueryWrapper<>();
            sysUserRoleQueryWrapper.eq("user_id",userId);
            List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(sysUserRoleQueryWrapper);
            map.put("username",username);
            map.put("userId",userId);
            map.put("perms",perms);
            String token = JwtService.createToken(map);
            Map<String,Object> getMap=new HashMap<>();
            getMap.put("username",username);
            getMap.put("userId",userId);
            getMap.put("token", token);
            getMap.put("avatar",avatar);
            if (sysUserRoles!=null){
                List<Integer> roleIds=new ArrayList<>();
                for (SysUserRole sysUserRole : sysUserRoles) {
                    roleIds.add(sysUserRole.getRoleId());
                }
                getMap.put("roleIds",roleIds);
            }
            Result result = Result.OK(getMap);
            result.setMessage("登陆成功");
            response.setContentType("application/json;charset=utf-8");
            PrintWriter writer = response.getWriter();
            writer.write(JSON.toJSONString(result));
        }
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        // 尝试从请求中获取用户名
        String username = request.getParameter("username");
        if (username == null || username.trim().isEmpty()) {
            username = "unknown";
        }

        // 记录登录失败日志
        operationLogService.recordLog(username, null, 1, "system",
                "用户登录失败：用户名或密码错误", 2, request);

        Result result = Result.ERROR("用户名或密码错误");
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }

    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException, ServletException {
        Result result = Result.ERROR("您没有权限，请重新登录");
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }

    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
        Result result = Result.ERROR("你没有登录，请先登录");
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }

    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        Result result = Result.OK("登出成功");
        response.setContentType("application/json;charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }

    public AuthorizationDecision check(Supplier<Authentication> authentication, RequestAuthorizationContext object) {
        Object user = authentication.get().getPrincipal();
        if (user instanceof String) {
            return new AuthorizationDecision(false);
        }
        ;
        SysUser u = (SysUser) user;
        List<String> perms = u.getPerms();
        QueryWrapper<SysMenu> sysMenuQueryWrapper = new QueryWrapper<>();
        HttpServletRequest request = object.getRequest();
        String url = request.getRequestURI();
        sysMenuQueryWrapper.eq("path", url);

        // Changed from selectOne to selectList to handle multiple menu items with the same path
        List<SysMenu> sysMenus = sysMenuMapper.selectList(sysMenuQueryWrapper);

        // Check if any of the menus have permissions that match the user's permissions
        if (sysMenus != null && !sysMenus.isEmpty()) {
            for (SysMenu sysMenu : sysMenus) {
                String menuPerms = sysMenu.getPerms();
                if (menuPerms != null && perms.contains(menuPerms)) {
                    return new AuthorizationDecision(true);
                }
            }
        }


        return new AuthorizationDecision(false);
    }
}
