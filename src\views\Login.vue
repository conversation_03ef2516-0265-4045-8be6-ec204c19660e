<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { login } from '@/api/system/user.js';
  import { ElMessage } from 'element-plus';
  import { useCounterStore } from '@/stores/counter.js';

  const router = useRouter();

  const loginForm = reactive({
    username: '',
    password: '',
    captcha: '',
    rememberMe: false
  });

  const loading = ref(false);
  const loginError = ref('');
  const showWelcome = ref(false);

  // 验证码相关
  const captchaText = ref('4dgw');
  const captchaDots = ref([]);
  const captchaLines = ref([]);

  // 更新轮播图相关变量，使用渐变背景而不是图片
  const carouselItems = ref([
    {
      title: '欢迎使用学生积分管理系统',
      subtitle: '高效、便捷的学生积分管理解决方案',
      color: 'linear-gradient(135deg, #3a7bd5, #00d2ff)',
      icon: 'chart-pie'
    },
    {
      title: '实时管理学生积分',
      subtitle: '随时了解学生成绩与表现',
      color: 'linear-gradient(135deg, #4568dc, #b06ab3)',
      icon: 'user-graduate'
    },
    {
      title: '数据驱动教学决策',
      subtitle: '可视化分析提升教学效果',
      color: 'linear-gradient(135deg, #43cea2, #185a9d)',
      icon: 'chart-line'
    },
    {
      title: '高效学生成长追踪',
      subtitle: '记录学生成长历程',
      color: 'linear-gradient(135deg, #834d9b, #d04ed6)',
      icon: 'chart-bar'
    }
  ]);
  const currentSlide = ref(0);

  onMounted(() => {
    setTimeout(() => {
      showWelcome.value = true;
    }, 300);

    refreshCaptcha();

    // 启动轮播图
    setInterval(() => {
      nextSlide();
    }, 5000);
  });

  const handleLogin = () => {
    if (!loginForm.username || !loginForm.password || !loginForm.captcha) {
      loginError.value = '请填写完整的登录信息';
      return;
    }

    // 验证验证码
    if (loginForm.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {
      loginError.value = '验证码错误';
      refreshCaptcha();
      return;
    }

    loading.value = true;
    loginError.value = '';

    // 调用登录API
    login(loginForm.username, loginForm.password)
      .then((res) => {
        if (res.data.code === 200) {
          // 保存JWT令牌到localStorage
          const token = res.data.data.token;
          localStorage.setItem('avatar', res.data.data.avatar);
          localStorage.setItem('username', res.data.data.username);
          localStorage.setItem('roleId', res.data.data.roleIds);
          // 确保token有Bearer前缀
          if (!token.startsWith('Bearer ')) {
            localStorage.setItem('Authorization', `Bearer ${token}`);
          } else {
            localStorage.setItem('Authorization', token);
          }

          ElMessage.success(res.data.message || '登录成功');

          // 重定向到首页，使用完整路径而不是相对路径
          router.push({ path: '/dashboard' });
        } else {
          loginError.value = res.data.message || '登录失败';
          refreshCaptcha();
        }
      })
      .catch((error) => {
        console.error('登录失败:', error);
        loginError.value = '服务器错误，请稍后再试';
        refreshCaptcha();
      })
      .finally(() => {
        loading.value = false; // 确保无论成功还是失败，都会重置loading状态
      });
  };

  // 生成更少的干扰点和线条
  const refreshCaptcha = () => {
    // 生成随机验证码
    const chars = 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let result = '';
    for (let i = 0; i < 4; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    captchaText.value = result;

    // 生成随机干扰点
    captchaDots.value = [];
    for (let i = 0; i < 12; i++) {
      captchaDots.value.push({
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        size: `${Math.random() * 3 + 1}px`
      });
    }

    // 生成随机干扰线
    captchaLines.value = [];
    for (let i = 0; i < 2; i++) {
      const angle = Math.random() * 360;
      captchaLines.value.push({
        left: `${Math.random() * 80}%`,
        top: `${Math.random() * 80}%`,
        width: `${Math.random() * 30 + 10}px`,
        height: `${Math.random() * 1 + 0.5}px`,
        color: `rgba(${Math.floor(Math.random() * 100)}, ${Math.floor(Math.random() * 100)}, ${Math.floor(Math.random() * 200)}, 0.3)`,
        angle: `${angle}deg`
      });
    }
  };

  // 切换到下一张幻灯片
  const nextSlide = () => {
    currentSlide.value = (currentSlide.value + 1) % carouselItems.value.length;
  };

  // 切换到上一张幻灯片
  const prevSlide = () => {
    currentSlide.value =
      (currentSlide.value - 1 + carouselItems.value.length) % carouselItems.value.length;
  };
  function guanfang() {
    router.push({ path: '/' });
  }
</script>

<template>
  <div class="login-container">
    <div class="login-overlay"></div>

    <div class="floating-particle particle-1"></div>
    <div class="floating-particle particle-2"></div>
    <div class="floating-particle particle-3"></div>
    <div class="floating-particle particle-4"></div>
    <div class="floating-particle particle-5"></div>

    <div class="login-content">
      <div class="login-left" :class="{ show: showWelcome }">
        <div class="system-logo">
          <img src="/images/logo.png" alt="Logo" class="logo-image" @click="guanfang" />
          <div class="logo-text">
            <h2>学生积分管理系统</h2>
            <p>STUDENT POINTS MANAGEMENT</p>
          </div>
        </div>

        <div class="login-card">
          <div class="card-header">
            <h1>系统登录</h1>
            <div class="header-underline"></div>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <div class="input-container">
                <span class="input-icon">
                  <i class="fas fa-user"></i>
                </span>
                <input
                  type="text"
                  v-model="loginForm.username"
                  placeholder="请输入用户名"
                  required
                />
              </div>
            </div>

            <div class="form-group">
              <div class="input-container">
                <span class="input-icon">
                  <i class="fas fa-lock"></i>
                </span>
                <input
                  type="password"
                  v-model="loginForm.password"
                  placeholder="请输入密码"
                  required
                />
              </div>
            </div>

            <div class="form-group captcha-group">
              <div class="input-container captcha-input-container">
                <span class="input-icon">
                  <i class="fas fa-shield-alt"></i>
                </span>
                <input
                  type="text"
                  v-model="loginForm.captcha"
                  placeholder="验证码"
                  required
                  class="captcha-input"
                />
              </div>
              <div class="captcha-box" @click="refreshCaptcha">
                <span class="captcha-text" :data-content="captchaText">{{ captchaText }}</span>
                <div class="captcha-noise">
                  <div
                    v-for="(dot, index) in captchaDots"
                    :key="`dot-${index}`"
                    class="noise-dot"
                    :style="{
                      left: dot.left,
                      top: dot.top,
                      width: dot.size,
                      height: dot.size
                    }"
                  ></div>
                  <div
                    v-for="(line, index) in captchaLines"
                    :key="`line-${index}`"
                    class="noise-line"
                    :style="{
                      left: line.left,
                      top: line.top,
                      width: line.width,
                      height: line.height,
                      background: line.color,
                      transform: `rotate(${line.angle})`
                    }"
                  ></div>
                </div>
              </div>
            </div>

            <div class="login-options">
              <div class="remember-password">
                <label class="remember-me">
                  <input type="checkbox" v-model="loginForm.rememberMe" />
                  <span class="checkbox-custom"></span>
                  <span>记住密码</span>
                </label>
              </div>
              <div class="account-actions">
                <a href="#" class="forgot-link">忘记密码?</a>
                <a href="#" class="find-account">找回账号</a>
              </div>
            </div>

            <!-- 添加固定高度的错误消息容器 -->
            <div class="error-message-container">
              <div v-if="loginError" class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <span>{{ loginError }}</span>
              </div>
            </div>

            <div class="button-container">
              <button type="submit" class="login-button" :disabled="loading">
                <span v-if="!loading">登 录</span>
                <span v-else class="button-loader"></span>
              </button>
            </div>
          </form>
        </div>

        <div class="login-footer">
          <p>© {{ new Date().getFullYear() }} 学生积分管理系统 - 版权所有</p>
        </div>
      </div>

      <div class="login-right">
        <div class="carousel">
          <div class="carousel-inner">
            <div
              v-for="(item, index) in carouselItems"
              :key="index"
              class="carousel-slide"
              :class="{ active: index === currentSlide }"
              :style="{ background: item.color }"
            >
              <div class="carousel-content">
                <div class="slide-icon">
                  <i :class="`fas fa-${item.icon}`"></i>
                </div>
                <h1>{{ item.title }}</h1>
                <p>{{ item.subtitle }}</p>
              </div>
              <div class="carousel-overlay"></div>
            </div>
          </div>
          <div class="carousel-controls">
            <button class="carousel-control prev" @click="prevSlide">
              <i class="fas fa-chevron-left"></i>
            </button>
            <div class="carousel-indicators">
              <span
                v-for="(_, index) in carouselItems"
                :key="index"
                class="indicator"
                :class="{ active: index === currentSlide }"
                @click="currentSlide = index"
              ></span>
            </div>
            <button class="carousel-control next" @click="nextSlide">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4ecf7 50%, #d6e4f0 100%);
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
  }

  .login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at bottom,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.1) 100%
    );
    backdrop-filter: blur(0px);
    z-index: 1;
  }

  .login-content {
    display: flex;
    width: 90%;
    max-width: 1300px;
    height: 82vh;
    max-height: 750px;
    position: relative;
    z-index: 2;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .login-left {
    width: 50%;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 30px 50px;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
  }

  .login-left.show {
    opacity: 1;
    transform: translateX(0);
  }

  .login-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff, #3a7bd5);
    background-size: 200% 100%;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .system-logo {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .logo-image {
    width: 100px;
    height: 100px;
    object-fit: contain;
  }

  .logo-text {
    margin-left: 15px;
  }

  .logo-text h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #3a7bd5;
  }

  .logo-text p {
    margin: 0;
    font-size: 11px;
    color: #666;
    letter-spacing: 0.5px;
  }

  .login-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 0 40px;
    margin-top: 0;
    min-height: 380px;
  }

  .card-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .card-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
  }

  .header-underline {
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    margin: 0 auto;
    border-radius: 2px;
  }

  .login-form {
    display: flex;
    flex-direction: column;
    gap: 14px;
  }

  .form-group {
    position: relative;
    margin-bottom: 0;
  }

  .input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .input-icon {
    position: absolute;
    left: 15px;
    color: #a0a0a0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .input-icon i {
    font-size: 16px;
  }

  input {
    width: 100%;
    padding: 14px 15px 14px 45px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s;
    background-color: #f9f9f9;
  }

  input:focus {
    outline: none;
    border-color: #3a7bd5;
    box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.1);
    background-color: #fff;
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0 12px;
  }

  .remember-password {
    display: flex;
  }

  .account-actions {
    display: flex;
    gap: 15px;
  }

  .remember-me {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    color: #606266;
    position: relative;
    font-size: 12px;
  }

  .remember-me input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkbox-custom {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 2px;
    transition: all 0.3s;
  }

  .remember-me input:checked ~ .checkbox-custom {
    background-color: #3a7bd5;
    border-color: #3a7bd5;
  }

  .checkbox-custom:after {
    content: '';
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .remember-me input:checked ~ .checkbox-custom:after {
    display: block;
  }

  .forgot-link {
    font-size: 12px;
    color: #3a7bd5;
    text-decoration: none;
    transition: color 0.3s;
    margin-right: 5px;
  }

  .find-account {
    color: #3a7bd5;
    border: 1px solid #3a7bd5;
    padding: 3px 8px;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    white-space: nowrap;
  }

  .forgot-link:hover,
  .find-account:hover {
    color: #00d2ff;
  }

  .find-account:hover {
    border-color: #00d2ff;
    background-color: rgba(0, 210, 255, 0.05);
  }

  .captcha-group {
    display: flex;
    gap: 10px;
    margin-bottom: 0;
  }

  .captcha-input-container {
    flex: 1;
  }

  .captcha-box {
    width: 110px;
    height: 42px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    transition: all 0.3s;
    border: 1px solid #ddd;
  }

  .captcha-box:hover {
    border-color: #3a7bd5;
  }

  .captcha-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      rgba(0, 0, 0, 0.03),
      rgba(0, 0, 0, 0.03) 5px,
      rgba(0, 0, 0, 0.01) 5px,
      rgba(0, 0, 0, 0.01) 10px
    );
  }

  .captcha-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 60%, rgba(255, 255, 255, 0.3) 0%, transparent 10%),
      radial-gradient(circle at 70% 40%, rgba(255, 255, 255, 0.3) 0%, transparent 15%);
    pointer-events: none;
  }

  .captcha-text {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: bold;
    color: #3a7bd5;
    letter-spacing: 2px;
    transform: skew(-5deg);
    user-select: none;
    position: relative;
    font-style: italic;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
    filter: blur(0.5px);
    opacity: 0.9;
    mix-blend-mode: multiply;
  }

  .captcha-text::before {
    content: attr(data-content);
    position: absolute;
    left: -2px;
    top: -2px;
    color: rgba(255, 0, 0, 0.3);
    filter: blur(1px);
  }

  .captcha-text::after {
    content: attr(data-content);
    position: absolute;
    left: 2px;
    top: 2px;
    color: rgba(0, 0, 255, 0.3);
    filter: blur(1px);
  }

  /* 添加错误消息容器的固定高度样式 */
  .error-message-container {
    min-height: 24px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .error-message {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #ff4d4f;
    font-size: 13px;
    padding: 8px 12px;
    background-color: rgba(255, 77, 79, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ff4d4f;
    width: 100%;
    animation: fadeIn 0.3s ease-in-out;
  }

  .error-message i {
    font-size: 14px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .login-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(90deg, #3a7bd5, #00d2ff);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(58, 123, 213, 0.2);
    max-height: 45px;
  }

  .login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s;
  }

  .login-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(58, 123, 213, 0.3);
  }

  .login-button:hover::before {
    left: 100%;
  }

  .login-button:disabled {
    background: linear-gradient(90deg, #a0cfff, #c2dfff);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .button-loader {
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .login-footer {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #909399;
  }

  .login-right {
    width: 50%;
    position: relative;
    overflow: hidden;
    transition: background 0.5s ease;
    background: linear-gradient(135deg, #4568dc 0%, #3a7bd5 50%, #00d2ff 100%);
  }

  .carousel {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .carousel-inner {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(1.05);
    transition:
      opacity 0.8s ease,
      transform 0.8s ease,
      background 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    backdrop-filter: blur(1px);
  }

  .carousel-slide.active {
    opacity: 1;
    transform: scale(1);
    z-index: 2;
  }

  .carousel-slide.active .carousel-content h1 {
    animation: slideUp 0.8s forwards ease-out;
  }

  .carousel-slide.active .carousel-content p {
    animation: slideUp 0.8s 0.2s forwards ease-out;
    opacity: 0;
  }

  .carousel-slide.active .slide-icon {
    animation:
      fadeZoom 0.8s forwards,
      float-icon 3s 0.8s ease-in-out infinite;
    opacity: 0;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeZoom {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 0.9;
      transform: scale(1);
    }
  }

  .carousel-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    padding: 30px;
    width: 80%;
    max-width: 450px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .carousel-content h1 {
    font-size: 36px;
    font-weight: 600;
    margin: 0 0 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .carousel-content p {
    font-size: 18px;
    opacity: 0.9;
    margin: 0;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .slide-icon {
    margin-bottom: 20px;
    font-size: 64px;
    opacity: 0.9;
    transform: translateY(0);
    animation: float-icon 3s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.15);
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto 20px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  @keyframes float-icon {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 1;
  }

  .carousel-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3;
  }

  .carousel-control {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
  }

  .carousel-control:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .carousel-indicators {
    display: flex;
    gap: 8px;
    margin: 0 15px;
  }

  .indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .indicator.active {
    background-color: white;
    width: 20px;
    border-radius: 4px;
  }

  .button-container {
    width: 100%;
    margin-top: 5px;
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
  }

  @media (max-width: 992px) {
    .login-content {
      width: 95%;
      flex-direction: column;
      height: auto;
      max-height: none;
    }

    .login-left,
    .login-right {
      width: 100%;
    }

    .login-right {
      height: 250px;
      order: -1;
    }

    .login-left {
      padding: 20px 30px;
    }

    .carousel-controls {
      bottom: 15px;
    }

    .login-card {
      padding: 10px 10px;
    }

    .system-logo {
      justify-content: center;
    }
  }

  @media (max-width: 576px) {
    .carousel-content h1 {
      font-size: 24px;
    }

    .carousel-content p {
      font-size: 14px;
    }

    .logo-image {
      width: 80px;
      height: 80px;
    }

    .login-left {
      padding: 20px;
    }

    .login-content {
      width: 100%;
      height: 100vh;
      max-height: none;
      border-radius: 0;
    }
  }

  /* 添加更多动态背景元素 */
  .floating-particle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(to right, rgba(58, 123, 213, 0.3), rgba(0, 210, 255, 0.3));
    animation: float 15s infinite ease-in-out;
    z-index: 0;
  }

  .particle-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: 10%;
    animation-duration: 20s;
    filter: blur(60px);
    opacity: 0.5;
  }

  .particle-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    right: 20%;
    animation-duration: 25s;
    animation-delay: 2s;
    filter: blur(40px);
    opacity: 0.4;
  }

  .particle-3 {
    width: 150px;
    height: 150px;
    top: 20%;
    right: -50px;
    animation-duration: 30s;
    animation-delay: 1s;
    filter: blur(30px);
    opacity: 0.3;
    background: linear-gradient(to right, rgba(176, 106, 179, 0.4), rgba(69, 104, 220, 0.4));
  }

  .particle-4 {
    width: 250px;
    height: 250px;
    bottom: 15%;
    left: -100px;
    animation-duration: 22s;
    animation-delay: 3s;
    filter: blur(50px);
    opacity: 0.3;
    background: linear-gradient(to right, rgba(67, 206, 162, 0.4), rgba(24, 90, 157, 0.4));
  }

  .particle-5 {
    width: 180px;
    height: 180px;
    top: 40%;
    left: 30%;
    animation-duration: 18s;
    animation-delay: 4s;
    filter: blur(35px);
    opacity: 0.2;
  }

  @keyframes float {
    0%,
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(50px, 50px) rotate(5deg);
    }
    50% {
      transform: translate(20px, -30px) rotate(-5deg);
    }
    75% {
      transform: translate(-40px, 20px) rotate(3deg);
    }
  }

  .refresh-captcha {
    font-size: 12px;
    color: #3a7bd5;
    text-decoration: none;
    transition: color 0.3s;
  }

  .refresh-captcha:hover {
    color: #00d2ff;
    text-decoration: underline;
  }

  .captcha-noise {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .noise-dot {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 50%;
  }

  .noise-line {
    position: absolute;
    transform-origin: center;
    background-color: rgba(0, 0, 0, 0.3);
  }
</style>
