<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserDetail, updateUserInfo } from '@/api/system/user.js'
import { getCurrentUser } from '@/utils/userUtils'
import axios from "axios";

const userInfo = ref({
  userId: null,
  username: '',
  realName: '',
  email: '',
  phone: '',
  avatar: '',
  userType: null,
  createTime: '',
  loginDate: '',
  roleNames:[]
})

const loading = ref(false)
const formRef = ref(null)

// 用户类型映射


// 表单验证规则
const rules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 获取当前用户信息
const fetchUserInfo = async () => {
  loading.value = true
  try {
    getUserDetail().then((res)=>{
      if (res.data.code === 200) {
        userInfo.value = res.data.data
      } else {
        ElMessage.error(res.data.message || '获取用户信息失败')
      }
    })
  } catch (error) {
    console.error('获取用户信息出错:', error)
    ElMessage.error('获取用户信息失败，请重试')
  } finally {
    loading.value = false
  }
}

// 更新用户信息
const updateProfile = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const res = await updateUserInfo(userInfo.value)
        if (res.data.code === 200) {
          ElMessage.success('个人信息更新成功')
        } else {
          ElMessage.error(res.data.message || '更新失败')
        }
      } catch (error) {
        console.error('更新个人信息出错:', error)
        ElMessage.error('更新个人信息失败，请重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 头像上传相关
const avatarUrl = ref('')
const headers = {
  Authorization:localStorage.getItem('Authorization')// 注意Bearer前缀
}
console.log('headers:', headers)
// 头像上传成功回调
const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    userInfo.value.avatar = response.data
    avatarUrl.value = response.data
    localStorage.setItem('avatar', response.data)
    ElMessage.success('头像上传成功')
    window.dispatchEvent(new Event('avatar-updated'))
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

// 头像上传前检查
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像只能是 JPG 或 PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 生命周期
onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="user-profile-container">
    <div class="page-header">
      <h2>个人信息</h2>
    </div>
    
    <el-card class="profile-card" v-loading="loading">
      <el-form
        ref="formRef"
        :model="userInfo"
        :rules="rules"
        label-width="100px"
      >
        <div class="avatar-section">
          <el-upload
            class="avatar-uploader"
            action="/api/minio/uploadFile"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="avatar-text">
            <h3>{{ userInfo.username }}</h3>
            <p v-for="roleName in userInfo.roleNames">{{roleName}}</p>
          </div>
        </div>

        <el-divider />
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名">
              <el-input v-model="userInfo.username" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="realName"  >
              <el-input v-model="userInfo.realName" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userInfo.email" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userInfo.phone" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间">
              <el-input v-model="userInfo.createTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最后登录">
              <el-input v-model="userInfo.loginDate" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="updateProfile">保存修改</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.user-profile-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.profile-card {
  margin-bottom: 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-text {
  margin-left: 20px;
}

.avatar-text h3 {
  margin: 0;
  font-size: 20px;
}

.avatar-text p {
  margin: 5px 0 0;
  color: #666;
}

.avatar-uploader {
  position: relative;
  overflow: hidden;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>