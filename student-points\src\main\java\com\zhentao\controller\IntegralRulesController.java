package com.zhentao.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.pojo.IntegralRules;
import com.zhentao.service.IntegralRulesService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@RequestMapping("/integral-rules")
public class IntegralRulesController {
    @Autowired
    private IntegralRulesService integralRulesService;
    @RequestMapping("findAllJia")
    public Result findAllJia(@RequestBody IntegralRules integralRules) {
        Page<IntegralRules> all = integralRulesService.findAllJia(integralRules);
        return all!=null ? Result.OK(all) : Result.ERROR();
    }
    @RequestMapping("findAllJian")
    public Result findAllJian(@RequestBody IntegralRules integralRules) {
        Page<IntegralRules> all = integralRulesService.findAllJian(integralRules);
        return all!=null ? Result.OK(all) : Result.ERROR();
    }
    @RequestMapping("addRules")
    public Result addRules(@RequestBody IntegralRules integralRules) {
        boolean save = integralRulesService.save(integralRules);
        return save ? Result.OK() : Result.ERROR();
    }
    @RequestMapping("updateRules")
    public Result updateRules(@RequestBody IntegralRules integralRules) {
        boolean update = integralRulesService.updateById(integralRules);
        return update ? Result.OK() : Result.ERROR();
    }
    @RequestMapping("delRules")
    public Result delRules(Integer id) {
        boolean remove = integralRulesService.removeById(id);
        return remove ? Result.OK() : Result.ERROR();
    }
    @RequestMapping("findById")
    public Result findById(Integer id) {
        IntegralRules integralRules = integralRulesService.getById(id);
        return integralRules!=null ? Result.OK(integralRules) : Result.ERROR();
    }

}
