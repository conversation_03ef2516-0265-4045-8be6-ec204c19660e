<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete, Refresh, Search } from '@element-plus/icons-vue'

// 参数类型选项
const paramTypeOptions = [
  { value: 'system', label: '系统参数' },
  { value: 'business', label: '业务参数' },
  { value: 'points', label: '积分参数' },
  { value: 'approval', label: '审批参数' },
  { value: 'notification', label: '通知参数' }
]

// 搜索表单
const searchForm = reactive({
  key: '',
  name: '',
  type: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const formTitle = ref('添加参数')

// 表单数据
const form = reactive({
  id: '',
  key: '',
  name: '',
  value: '',
  type: '',
  description: '',
  isSystem: false
})

// 表单规则
const rules = {
  key: [
    { required: true, message: '请输入参数键', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_.]*$/, message: '参数键只能包含字母、数字、下划线和点，且必须以字母开头', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入参数名称', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择参数类型', trigger: 'change' }
  ]
}

const formRef = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchData()
})

// 方法定义
const fetchData = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    tableData.value = generateMockData()
    total.value = 100
    loading.value = false
  }, 500)
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const resetSearch = () => {
  searchForm.key = ''
  searchForm.name = ''
  searchForm.type = ''
  currentPage.value = 1
  fetchData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

const handleAdd = () => {
  dialogType.value = 'add'
  formTitle.value = '添加参数'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogType.value = 'edit'
  formTitle.value = '编辑参数'
  resetForm()
  
  // 填充表单数据
  Object.keys(form).forEach(key => {
    if (key in row) {
      form[key] = row[key]
    }
  })
  
  dialogVisible.value = true
}

const handleDelete = (row) => {
  if (row.isSystem) {
    ElMessage.warning('系统内置参数不可删除')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除参数 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 模拟删除操作
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value.splice(index, 1)
      total.value--
    }
    ElMessage.success('删除成功')
  })
  .catch(() => {})
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (dialogType.value === 'add') {
        // 模拟添加参数
        const newParam = {
          ...form,
          id: Date.now().toString(),
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString()
        }
        tableData.value.unshift(newParam)
        total.value++
        ElMessage.success('添加参数成功')
      } else {
        // 模拟编辑参数
        const index = tableData.value.findIndex(item => item.id === form.id)
        if (index !== -1) {
          tableData.value[index] = { 
            ...tableData.value[index], 
            ...form,
            updateTime: new Date().toLocaleString()
          }
          ElMessage.success('编辑参数成功')
        }
      }
      dialogVisible.value = false
    } else {
      return false
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  form.id = ''
  form.key = ''
  form.name = ''
  form.value = ''
  form.type = ''
  form.description = ''
  form.isSystem = false
}

const handleRefresh = () => {
  fetchData()
}

const getParamTypeLabel = (type) => {
  const option = paramTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

// 生成模拟数据
const generateMockData = () => {
  const systemParams = [
    {
      id: '1',
      key: 'system.name',
      name: '系统名称',
      value: '学生积分管理系统',
      type: 'system',
      description: '系统显示名称',
      isSystem: true,
      createTime: '2023-01-01 08:00:00',
      updateTime: '2023-01-01 08:00:00'
    },
    {
      id: '2',
      key: 'system.logo',
      name: '系统Logo',
      value: '/images/logo.png',
      type: 'system',
      description: '系统Logo路径',
      isSystem: true,
      createTime: '2023-01-01 08:00:00',
      updateTime: '2023-01-01 08:00:00'
    },
    {
      id: '3',
      key: 'system.version',
      name: '系统版本',
      value: 'v1.0.0',
      type: 'system',
      description: '系统当前版本号',
      isSystem: true,
      createTime: '2023-01-01 08:00:00',
      updateTime: '2023-01-01 08:00:00'
    }
  ]
  
  const businessParams = [
    {
      id: '4',
      key: 'business.school.name',
      name: '学校名称',
      value: 'XX大学',
      type: 'business',
      description: '学校名称',
      isSystem: false,
      createTime: '2023-01-02 09:00:00',
      updateTime: '2023-01-02 09:00:00'
    },
    {
      id: '5',
      key: 'business.semester.current',
      name: '当前学期',
      value: '2023-2024-1',
      type: 'business',
      description: '当前学期标识',
      isSystem: false,
      createTime: '2023-01-02 09:00:00',
      updateTime: '2023-01-02 09:00:00'
    }
  ]
  
  const pointsParams = [
    {
      id: '6',
      key: 'points.initial',
      name: '初始积分',
      value: '100',
      type: 'points',
      description: '学生初始积分值',
      isSystem: false,
      createTime: '2023-01-03 10:00:00',
      updateTime: '2023-01-03 10:00:00'
    },
    {
      id: '7',
      key: 'points.max.add',
      name: '单次最大增加积分',
      value: '50',
      type: 'points',
      description: '单次最大可增加的积分值',
      isSystem: false,
      createTime: '2023-01-03 10:00:00',
      updateTime: '2023-01-03 10:00:00'
    },
    {
      id: '8',
      key: 'points.max.deduct',
      name: '单次最大扣除积分',
      value: '30',
      type: 'points',
      description: '单次最大可扣除的积分值',
      isSystem: false,
      createTime: '2023-01-03 10:00:00',
      updateTime: '2023-01-03 10:00:00'
    }
  ]
  
  const approvalParams = [
    {
      id: '9',
      key: 'approval.auto.threshold',
      name: '自动审批阈值',
      value: '10',
      type: 'approval',
      description: '低于此积分值的申请自动审批通过',
      isSystem: false,
      createTime: '2023-01-04 11:00:00',
      updateTime: '2023-01-04 11:00:00'
    },
    {
      id: '10',
      key: 'approval.expire.days',
      name: '审批过期天数',
      value: '7',
      type: 'approval',
      description: '审批请求过期天数',
      isSystem: false,
      createTime: '2023-01-04 11:00:00',
      updateTime: '2023-01-04 11:00:00'
    }
  ]
  
  const notificationParams = [
    {
      id: '11',
      key: 'notification.email.enabled',
      name: '启用邮件通知',
      value: 'true',
      type: 'notification',
      description: '是否启用邮件通知',
      isSystem: false,
      createTime: '2023-01-05 12:00:00',
      updateTime: '2023-01-05 12:00:00'
    },
    {
      id: '12',
      key: 'notification.sms.enabled',
      name: '启用短信通知',
      value: 'false',
      type: 'notification',
      description: '是否启用短信通知',
      isSystem: false,
      createTime: '2023-01-05 12:00:00',
      updateTime: '2023-01-05 12:00:00'
    }
  ]
  
  return [...systemParams, ...businessParams, ...pointsParams, ...approvalParams, ...notificationParams]
}
</script>

<template>
  <div class="system-params-container">
    <div class="page-header">
      <h2>系统参数</h2>
      <el-button type="primary" :icon="Refresh" circle @click="handleRefresh" />
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :model="searchForm" inline>
        <el-form-item label="参数键">
          <el-input v-model="searchForm.key" placeholder="请输入参数键" clearable />
        </el-form-item>
        
        <el-form-item label="参数名称">
          <el-input v-model="searchForm.name" placeholder="请输入参数名称" clearable />
        </el-form-item>
        
        <el-form-item label="参数类型">
          <el-select v-model="searchForm.type" placeholder="请选择参数类型" clearable>
            <el-option
              v-for="item in paramTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" :icon="Plus" @click="handleAdd">添加参数</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="key" label="参数键" min-width="180" />
        <el-table-column prop="name" label="参数名称" min-width="150" />
        <el-table-column prop="value" label="参数值" min-width="150" />
        <el-table-column prop="type" label="参数类型" width="120">
          <template #default="scope">
            {{ getParamTypeLabel(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="参数描述" min-width="200" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button type="primary" link :icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              type="danger" 
              link 
              :icon="Delete" 
              @click="handleDelete(scope.row)"
              :disabled="scope.row.isSystem"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:currentPage="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="参数键" prop="key">
          <el-input v-model="form.key" :disabled="dialogType === 'edit' && form.isSystem" />
        </el-form-item>
        
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        
        <el-form-item label="参数值" prop="value">
          <el-input v-model="form.value" />
        </el-form-item>
        
        <el-form-item label="参数类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择参数类型" :disabled="dialogType === 'edit' && form.isSystem">
            <el-option
              v-for="item in paramTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="参数描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3" />
        </el-form-item>
        
        <el-form-item label="系统内置" v-if="dialogType === 'edit'">
          <el-switch v-model="form.isSystem" disabled />
          <span class="form-tip" v-if="form.isSystem">系统内置参数不可修改键名和类型</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.system-params-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.system-params-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.search-container, .table-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.search-container:hover, .table-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.form-tip {
  margin-left: 10px;
  color: #E6A23C;
  font-size: 12px;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>