package com.zhentao.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.dto.system.system.ApplicationDto;
import com.zhentao.dto.system.system.PageActivityApplicationDto;
import com.zhentao.pojo.ActivityApplication;
import com.zhentao.utils.Result;

/**
 * <p>
 * 活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public interface ActivityApplicationService extends IService<ActivityApplication> {
    /**
     * 报名活动
     */
    public Result signUpActivity(ApplicationDto applicationDto);
    /**
     * 取消报名
     */
    public Result cancelSignUpActivity(Integer applicationId, Integer activityId);
    /**
     * 获取活动报名列表
     */
    public Page<ActivityApplication> activityApplicationPage(PageActivityApplicationDto pageActivityApplicationDto);
    /**
     * 查询活动列表
     */
    public Result findActivity();
}
