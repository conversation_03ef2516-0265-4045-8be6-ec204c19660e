package com.zhentao.controller;

import com.zhentao.config.MinioConfig;
import com.zhentao.pojo.SysUser;
import com.zhentao.service.SysUserService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

@RestController
@RequestMapping("/minio")
public class MinioController {
    @Autowired
    private MinioConfig minioConfig;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private MinioClient minioClient;
    //上传图片切回显
    @PostMapping("/uploadFile")
    public Result uploadFile(@RequestParam("file") MultipartFile file) {

        try {
            // 1. 验证 MinIO 连接


            // 2. 检查存储桶是否存在
            boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucket())
                    .build());
            if (!isExist) {
                return Result.ERROR("存储桶不存在");
            }
            //1.检查文件是否为空
            if (file.isEmpty()){
                return Result.ERROR("上传图片不能为空");
            }
            // 2. 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID().toString() + extension;

            // 3. 上传文件到minio
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucket())
                            .object(fileName)
                            .contentType(file.getContentType())
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .build()
            );


            // 4. 获取文件访问URL
            String fileUrl = minioClient.getObjectUrl(minioConfig.getBucket(), fileName);
            String cleanUrl = fileUrl.split("\\?")[0];
            System.out.println("文件访问地址: " + cleanUrl);

            // 6. 保存到数据库
            Integer userId = UserContext.getCurrentUser().getUserId();
            SysUser user = sysUserService.getById(userId);
            user.setAvatar(cleanUrl);
            sysUserService.updateById(user);
           return Result.OK(cleanUrl);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("上传失败: " + e.getMessage());
        }
    }

}
