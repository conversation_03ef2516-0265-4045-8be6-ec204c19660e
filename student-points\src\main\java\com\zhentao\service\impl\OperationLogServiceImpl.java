package com.zhentao.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.mapper.OperationLogMapper;
import com.zhentao.pojo.OperationLog;
import com.zhentao.service.OperationLogService;
import com.zhentao.utils.ClientInfoUtil;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {
    @Autowired
    private OperationLogMapper operationLogMapper;

    @Override
    public Result findLogPage(@RequestBody OperationLog operationLog) {
        // 设置默认分页参数
        int pageNum = operationLog.getPageNum() != null ? operationLog.getPageNum() : 1;
        int pageSize = operationLog.getPageSize() != null ? operationLog.getPageSize() : 10;

        Page<OperationLog> page = new Page<>(pageNum, pageSize);
        QueryWrapper<OperationLog> wrapper = new QueryWrapper<>();

        // 用户名模糊查询
        if (operationLog.getUsername() != null && !operationLog.getUsername().trim().isEmpty()) {
            wrapper.like("username", operationLog.getUsername().trim());
        }

        // 真实姓名模糊查询
        if (operationLog.getRealName() != null && !operationLog.getRealName().trim().isEmpty()) {
            wrapper.like("real_name", operationLog.getRealName().trim());
        }

        // 操作类型精确查询
        if (operationLog.getOperationType() != null) {
            wrapper.eq("operation_type", operationLog.getOperationType());
        }

        // 操作模块模糊查询
        if (operationLog.getModule() != null && !operationLog.getModule().trim().isEmpty()) {
            wrapper.like("module", operationLog.getModule().trim());
        }

        // 操作状态精确查询
        if (operationLog.getStatus() != null) {
            wrapper.eq("status", operationLog.getStatus());
        }

        // IP地址精确查询
        if (operationLog.getIp() != null && !operationLog.getIp().trim().isEmpty()) {
            wrapper.eq("ip", operationLog.getIp().trim());
        }

        // 操作描述模糊查询
        if (operationLog.getDescription() != null && !operationLog.getDescription().trim().isEmpty()) {
            wrapper.like("description", operationLog.getDescription().trim());
        }

        // 时间范围查询
        if (operationLog.getStartTime() != null) {
            wrapper.ge("operation_time", operationLog.getStartTime());
        }
        if (operationLog.getEndTime() != null) {
            wrapper.le("operation_time", operationLog.getEndTime());
        }

        // 按操作时间倒序排列，最新的在前面
        wrapper.orderByDesc("operation_time");

        Page<OperationLog> operationLogPage = operationLogMapper.selectPage(page, wrapper);
        return operationLogPage != null ? Result.OK(operationLogPage) : Result.ERROR();
    }

    @Override
    public void recordLog(String username, String realName, Integer operationType,
                         String module, String description, Integer status,
                         HttpServletRequest request) {
        try {
            OperationLog operationLog = new OperationLog();
            operationLog.setUsername(username);
            operationLog.setRealName(realName);
            operationLog.setOperationType(operationType);
            operationLog.setModule(module);
            operationLog.setDescription(description);
            operationLog.setStatus(status);
            operationLog.setOperationTime(LocalDateTime.now());

            // 获取客户端信息
            if (request != null) {
                operationLog.setIp(ClientInfoUtil.getClientIp(request));
                operationLog.setBrowser(ClientInfoUtil.getBrowserInfo(request));
                operationLog.setOs(ClientInfoUtil.getOperatingSystem(request));
            } else {
                operationLog.setIp("unknown");
                operationLog.setBrowser("unknown");
                operationLog.setOs("unknown");
            }

            // 保存日志
            operationLogMapper.insert(operationLog);
        } catch (Exception e) {
            // 记录日志失败不应该影响主业务流程，只打印错误日志
            System.err.println("记录操作日志失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Result clearAllLogs() {
        try {
            // 删除所有日志记录
            QueryWrapper<OperationLog> wrapper = new QueryWrapper<>();
            int deletedCount = operationLogMapper.delete(wrapper);

            // 记录清空日志的操作
            try {
                String currentUsername = "system";
                String currentRealName = "系统管理员";

                // 尝试获取当前用户信息
                if (UserContext.getCurrentUser() != null) {
                    currentUsername = UserContext.getCurrentUser().getUsername();
                    currentRealName = UserContext.getCurrentUser().getRealName();
                }

                recordLog(currentUsername, currentRealName, 5, "system",
                    "清空操作日志，共删除 " + deletedCount + " 条记录", 1, null);
            } catch (Exception e) {
                // 记录操作日志失败不影响主业务
                System.err.println("记录清空日志操作失败: " + e.getMessage());
            }

            return Result.OK("成功清空 " + deletedCount + " 条日志记录");
        } catch (Exception e) {
            System.err.println("清空日志失败: " + e.getMessage());
            return Result.ERROR("清空日志失败: " + e.getMessage());
        }
    }

    @Override
    public Result exportLogs(OperationLog operationLog) {
        try {
            QueryWrapper<OperationLog> wrapper = new QueryWrapper<>();

            // 应用查询条件（复用查询逻辑）
            if (operationLog.getUsername() != null && !operationLog.getUsername().trim().isEmpty()) {
                wrapper.like("username", operationLog.getUsername().trim());
            }
            if (operationLog.getRealName() != null && !operationLog.getRealName().trim().isEmpty()) {
                wrapper.like("real_name", operationLog.getRealName().trim());
            }
            if (operationLog.getOperationType() != null) {
                wrapper.eq("operation_type", operationLog.getOperationType());
            }
            if (operationLog.getModule() != null && !operationLog.getModule().trim().isEmpty()) {
                wrapper.like("module", operationLog.getModule().trim());
            }
            if (operationLog.getStatus() != null) {
                wrapper.eq("status", operationLog.getStatus());
            }
            if (operationLog.getIp() != null && !operationLog.getIp().trim().isEmpty()) {
                wrapper.eq("ip", operationLog.getIp().trim());
            }
            if (operationLog.getDescription() != null && !operationLog.getDescription().trim().isEmpty()) {
                wrapper.like("description", operationLog.getDescription().trim());
            }
            if (operationLog.getStartTime() != null) {
                wrapper.ge("operation_time", operationLog.getStartTime());
            }
            if (operationLog.getEndTime() != null) {
                wrapper.le("operation_time", operationLog.getEndTime());
            }

            // 按操作时间倒序排列
            wrapper.orderByDesc("operation_time");

            // 查询所有符合条件的日志（不分页）
            List<OperationLog> logs = operationLogMapper.selectList(wrapper);

            // 记录导出操作
            try {
                String currentUsername = "system";
                String currentRealName = "系统管理员";

                if (UserContext.getCurrentUser() != null) {
                    currentUsername = UserContext.getCurrentUser().getUsername();
                    currentRealName = UserContext.getCurrentUser().getRealName();
                }

                recordLog(currentUsername, currentRealName, 8, "system",
                    "导出操作日志，共导出 " + logs.size() + " 条记录", 1, null);
            } catch (Exception e) {
                System.err.println("记录导出日志操作失败: " + e.getMessage());
            }

            return Result.OK(logs);
        } catch (Exception e) {
            System.err.println("导出日志失败: " + e.getMessage());
            return Result.ERROR("导出日志失败: " + e.getMessage());
        }
    }

    @Override
    public Result batchDeleteLogs(List<Integer> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.ERROR("请选择要删除的日志记录");
            }

            // 批量删除日志记录
            int deletedCount = operationLogMapper.deleteBatchIds(ids);

            // 记录批量删除的操作
            try {
                String currentUsername = "system";
                String currentRealName = "系统管理员";

                // 尝试获取当前用户信息
                if (UserContext.getCurrentUser() != null) {
                    currentUsername = UserContext.getCurrentUser().getUsername();
                    currentRealName = UserContext.getCurrentUser().getRealName();
                }

                recordLog(currentUsername, currentRealName, 5, "system",
                    "批量删除操作日志，共删除 " + deletedCount + " 条记录", 1, null);
            } catch (Exception e) {
                // 记录操作日志失败不影响主业务
                System.err.println("记录批量删除日志操作失败: " + e.getMessage());
            }

            return Result.OK("成功删除 " + deletedCount + " 条日志记录");
        } catch (Exception e) {
            System.err.println("批量删除日志失败: " + e.getMessage());
            return Result.ERROR("批量删除日志失败: " + e.getMessage());
        }
    }
}
