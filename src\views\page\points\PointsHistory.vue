<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Picture } from '@element-plus/icons-vue'
import { searchStudents, getPointsHistory, getPointsStatistics, cancelPointsApplication } from '@/api/system/points.js'
import * as XLSX from 'xlsx'

// 筛选表单
const filterForm = reactive({
  studentId: '',
  className: '',
  type: '',
  category: '',
  status: '',
  dateRange: []
})

// 学生搜索
const studentSearchLoading = ref(false)
const studentOptions = ref([])

// 班级选项

// 状态选项
const statusOptions = [
  { value: 'canceled', label: '已撤销' },
  { value: 'pending', label: '待审核' },
  { value: 'approved', label: '已通过' },
  { value: 'rejected', label: '已拒绝' }
]

// 积分类别选项
const pointsCategories = ref([
  {
    label: '加分项目',
    options: [
      { value: 'exam_excellent', label: '期末考试优秀' },
      { value: 'competition_award', label: '获得学科竞赛奖项' },
      { value: 'homework_quality', label: '完成高质量课程作业' },
      { value: 'class_discussion', label: '积极参与课堂讨论' },
      { value: 'volunteer_service', label: '参与志愿服务' },
      { value: 'community_activity', label: '参加社区活动' },
      { value: 'campus_culture', label: '参与校园文化建设' },
      { value: 'academic_paper', label: '发表学术论文' },
      { value: 'innovation_project', label: '参与创新项目' },
      { value: 'patent', label: '获得专利' },
      { value: 'student_cadre', label: '担任学生干部' },
      { value: 'honor_title', label: '获得荣誉称号' },
      { value: 'other_excellent', label: '其他优秀表现' }
    ]
  },
  {
    label: '扣分项目',
    options: [
      { value: 'class_late', label: '课堂迟到' },
      { value: 'class_absence', label: '课堂缺勤' },
      { value: 'class_disorder', label: '课堂扰乱秩序' },
      { value: 'phone_usage', label: '使用手机' },
      { value: 'dorm_hygiene', label: '宿舍卫生不合格' },
      { value: 'dorm_absence', label: '夜不归宿' },
      { value: 'illegal_electricity', label: '违规用电' },
      { value: 'exam_late', label: '考试迟到' },
      { value: 'exam_cheating', label: '考试作弊' },
      { value: 'exam_substitute', label: '代考' },
      { value: 'fighting', label: '打架斗殴' },
      { value: 'property_damage', label: '损坏公物' },
      { value: 'other_violation', label: '其他违规行为' }
    ]
  }
])

// 表格数据
const loading = ref(false)
const statsLoading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(0)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref({})

// 统计数据
const statisticsData = ref({
  totalAddPoints: 0,
  totalDeductPoints: 0,
  netPoints: 0,
  recordCount: 0
})

// 计算属性
const filteredTableData = computed(() => {
  return tableData.value;
})

// 移除pagedTableData计算属性，因为我们现在使用后端分页
// const pagedTableData = computed(() => {
//   const start = (currentPage.value - 1) * pageSize.value
//   const end = start + pageSize.value
//   return filteredTableData.value.slice(start, end)
// })

// 统计信息
const totalAddPoints = computed(() => statisticsData.value.totalAddPoints || 0)
const totalDeductPoints = computed(() => statisticsData.value.totalDeductPoints || 0)
const netPoints = computed(() => statisticsData.value.netPoints || 0)

// 生命周期钩子
onMounted(() => {
  loadRecords()
})

// 方法定义
const remoteStudentSearch = (query) => {
  if (query) {
    studentSearchLoading.value = true
    searchStudents(query).then(response => {
      console.log('Student search response:', response)
      // 修复响应处理：检查响应结构
      if (response.status === 200 && response.data) {
        // 处理后端返回的数据结构: { code: 200, message: '操作成功', data: Array(10) }
        const responseData = response.data
        if (responseData.code === 200 && responseData.data) {
          const students = responseData.data
          studentOptions.value = students.map(student => ({
            id: student.studentId,
            name: student.realName,
            studentNo: student.studentNo,
            className: student.className || '未知班级',
            classId: student.classId // 保存班级ID
          }))
        } else {
          console.error('学生数据格式错误:', responseData)
          ElMessage.error(responseData.message || '学生信息获取失败')
          studentOptions.value = []
        }
      } else {
        console.error('学生搜索失败:', response)
        ElMessage.error('学生信息获取失败')
        studentOptions.value = []
      }
      studentSearchLoading.value = false
    }).catch(err => {
      console.error('学生搜索失败:', err)
      ElMessage.error('学生信息获取失败')
      studentSearchLoading.value = false
      studentOptions.value = []
    })
  } else {
    studentOptions.value = []
  }
}

const handleSearch = () => {
  currentPage.value = 1
  console.log('搜索参数:', filterForm)
  loadRecords()
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'dateRange') {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  currentPage.value = 1
  loadRecords()
}

// 修改loadRecords函数，确保在数据加载完成后更新统计信息
const loadRecords = () => {
  loading.value = true
  
  // 构建查询参数 - 将filterForm转换为PointsApply对象格式
  const params = {
    // 分页参数 - 确保是整数
    pageNum: parseInt(currentPage.value) || 1,
    pageSize: parseInt(pageSize.value) || 10,
    // 学生ID
    studentId: filterForm.studentId || null,
    // 班级名称
    className: filterForm.className || null,
    // 积分类型（1-加分，2-减分）
    pointsChange: filterForm.type === 'add' ? 1 : (filterForm.type === 'deduct' ? 2 : null),
    // 积分项目类别
    category: filterForm.category || null,
    // 状态
    status: filterForm.status ? mapStatusToCode(filterForm.status) : null,
    // 日期范围
    startTime: filterForm.dateRange && filterForm.dateRange.length > 0 ? filterForm.dateRange[0] : null,
    endTime: filterForm.dateRange && filterForm.dateRange.length > 0 ? filterForm.dateRange[1] : null
  }
  
  console.log('发送到后端的参数:', params)
  
  // 调用API获取历史记录
  getPointsHistory(params).then(response => {
    console.log('History response:', response)
    if (response.status === 200 && response.data) {
      const responseData = response.data
      if (responseData.code === 200 && responseData.data) {
        // 处理分页数据
        const pageData = responseData.data;
        console.log('分页数据:', pageData);
        
        // 获取记录列表
        const records = pageData.records || [];
        
        // 更新分页信息
        currentPage.value = pageData.current || 1;
        pageSize.value = pageData.size || 10;
        totalRecords.value = pageData.total || 0;
        
        // 处理返回的数据，添加type字段
        tableData.value = records.map(record => {
          // 根据pointsChange字段设置type
          const type = record.pointsChange === 1 ? 'add' : 'deduct';
          
          // 使用后端返回的studentName和className，如果没有则使用备用值
          const studentName = record.studentName || '未知学生';
          const className = record.className || '未知班级';
          
          // 确保积分值正确 - 使用数据库中的实际积分值
          const points = record.points || 0;
          
          // 处理图片URL
          let processedEvidenceImages = '';
          if (record.evidenceImages) {
            const urls = record.evidenceImages.split(',');
            processedEvidenceImages = urls.map(url => {
              const trimmedUrl = url.trim();
              // 检查URL类型并正确处理
              if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
                // 如果是MinIO URL或其他网络URL，直接使用
                return trimmedUrl;
              } else if (trimmedUrl.includes('\\') || trimmedUrl.includes('/')) {
                // 如果是文件路径，使用静态文件控制器
                return `/api/static/path?path=${encodeURIComponent(trimmedUrl)}`;
              } else {
                // 如果只是文件名，使用静态文件控制器的文件名端点
                return `/api/static/${encodeURIComponent(trimmedUrl)}`;
              }
            }).join(',');
          }
          
          return {
            ...record,
            type,
            studentName,
            className,
            points,
            processedEvidenceImages,
            // 使用后端返回的状态文本，如果没有则根据status转换
            status: record.statusText || (record.status !== undefined && record.status !== null ? mapStatusCodeToText(record.status) : 'unknown')
          };
        });
        console.log('处理后的数据:', tableData.value)
      } else {
        console.error('加载历史记录失败:', responseData)
        ElMessage.error(responseData.message || '获取积分历史记录失败')
        tableData.value = []
      }
    } else {
      console.error('加载历史记录失败:', response)
      ElMessage.error('获取积分历史记录失败')
      tableData.value = []
    }
    loading.value = false
    
    // 无论成功或失败，都更新统计数据
    loadStatistics()
  }).catch(err => {
    console.error('加载历史记录失败:', err)
    ElMessage.error('获取积分历史记录失败')
    tableData.value = []
    loading.value = false
    
    // 发生错误时也更新统计数据
    loadStatistics()
  })
}

// 添加状态码映射函数
const mapStatusToCode = (status) => {
  switch (status) {
    case 'pending': return 1
    case 'approved': return 2
    case 'rejected': return 3
    case 'canceled': return 0 // 已撤销状态为0
    default: return null
  }
}

// 添加状态码到文本的映射函数
const mapStatusCodeToText = (code) => {
  switch (parseInt(code)) {
    case 0: return 'canceled' // 已撤销
    case 1: return 'pending'
    case 2: return 'approved'
    case 3: return 'rejected'
    case 4: return 'canceled' // 兼容旧代码，之前用4表示已撤销
    default: return 'unknown'
  }
}

// 更新loadStatistics函数，添加加载状态
const loadStatistics = () => {
  // 构建查询参数，与历史记录使用相同的过滤条件
  const params = {
    // 使用与历史记录相同的过滤条件
    studentId: filterForm.studentId || null,
    className: filterForm.className || null,
    pointsChange: filterForm.type === 'add' ? 1 : (filterForm.type === 'deduct' ? 2 : null),
    category: filterForm.category || null,
    status: filterForm.status ? mapStatusToCode(filterForm.status) : null,
    startTime: filterForm.dateRange && filterForm.dateRange.length > 0 ? filterForm.dateRange[0] : null,
    endTime: filterForm.dateRange && filterForm.dateRange.length > 0 ? filterForm.dateRange[1] : null
  }
  
  console.log('统计查询参数:', params)
  
  // 设置加载状态
  statsLoading.value = true
  
  // 调用API获取统计数据
  getPointsStatistics(params).then(response => {
    console.log('Statistics response:', response)
    if (response.status === 200 && response.data) {
      const responseData = response.data
      if (responseData.code === 200 && responseData.data) {
        // 更新统计数据
        statisticsData.value = responseData.data
        // 设置记录总数
        statisticsData.value.recordCount = totalRecords.value
      } else {
        console.error('加载统计数据失败:', responseData)
        resetStatistics()
      }
    } else {
      console.error('加载统计数据失败:', response)
      resetStatistics()
    }
    // 清除加载状态
    statsLoading.value = false
  }).catch(err => {
    console.error('加载统计数据失败:', err)
    resetStatistics()
    // 清除加载状态
    statsLoading.value = false
  })
}

// 更新resetStatistics函数，使用totalRecords
const resetStatistics = () => {
  statisticsData.value = {
    totalAddPoints: 0,
    totalDeductPoints: 0,
    netPoints: 0,
    recordCount: totalRecords.value
  }
}

const refreshData = () => {
  loadRecords()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadRecords() // 页大小改变时重新加载数据
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadRecords() // 页码改变时重新加载数据
}

const getStatusType = (status) => {
  switch (status) {
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'pending': return 'warning'
    case 'canceled': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'pending': return '待审核'
    case 'canceled': return '已撤销'
    default: return '未知'
  }
}

// 修改viewDetail函数，正确处理图片URL
const viewDetail = (row) => {
  currentDetail.value = { ...row }
  
  // 如果有处理过的图片URLs，使用它们
  if (row.processedEvidenceImages) {
    currentDetail.value.evidenceImages = row.processedEvidenceImages
  } else if (row.evidenceImages) {
    // 处理原始图片URLs
    const urls = row.evidenceImages.split(',')
    currentDetail.value.evidenceImages = urls.map(url => {
      const trimmedUrl = url.trim()
      // 检查URL类型并正确处理
      if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
        // 如果是MinIO URL或其他网络URL，直接使用
        return trimmedUrl
      } else if (trimmedUrl.includes('\\') || trimmedUrl.includes('/')) {
        // 如果是文件路径，使用静态文件控制器
        return `/api/static/path?path=${encodeURIComponent(trimmedUrl)}`
      } else {
        // 如果只是文件名，使用静态文件控制器的文件名端点
        return `/api/static/${encodeURIComponent(trimmedUrl)}`
      }
    }).join(',')
  }
  
  detailDialogVisible.value = true
}

const getDetailTitle = (detail) => {
  if (!detail.type) return '积分详情'
  return detail.type === 'add' ? '积分添加详情' : '积分扣除详情'
}

// 添加导出Excel功能
const exportData = () => {
  try {
    // 准备导出数据
    const exportData = tableData.value.map(item => ({
      '记录ID': item.applyId || '',
      '学号': item.studentId || '',
      '学生姓名': item.studentName || '',
      '班级': item.className || '',
      '积分类型': item.type === 'add' ? '加分' : '扣分',
      '积分值': item.type === 'add' ? `+${item.points}` : `-${item.points}`,
      '原因': item.reason || '',
      '申请时间': item.createTime || '',
      '状态': getStatusText(item.status) || '',
      '审核时间': item.reviewTime || '',
      '审核意见': item.reviewComment || ''
    }));
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '积分历史记录');
    
    // 生成Excel文件并下载
    const fileName = `积分历史记录_${new Date().toISOString().split('T')[0]}.xlsx`;
    
    // 使用XLSX的方法直接下载文件
    XLSX.writeFile(workbook, fileName);
    
    ElMessage.success(`数据导出成功，文件已下载`);
  } catch (error) {
    console.error('导出Excel失败:', error);
    ElMessage.error('导出Excel失败: ' + error.message);
  }
}

// 格式化日期 YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return ''
  if (typeof date === 'string') return date
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

// 格式化日期时间 YYYY-MM-DD HH:MM:SS
const formatDateTime = (date) => {
  if (!date) return ''
  
  const formatted = formatDate(date)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${formatted} ${hours}:${minutes}:${seconds}`
}

// 添加图片错误处理函数
const handleImageError = (url) => {
  console.error('Detail image load error:', url);
  ElMessage.warning(`图片加载失败: ${url}`);
};

// 添加撤销申请功能
const cancelApplication = (row) => {
  ElMessageBox.confirm(
    `确定要撤销此${row.type === 'add' ? '积分添加' : '积分扣除'}申请吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 调用API撤销申请
    cancelPointsApplication(row.applyId).then(response => {
      if (response.status === 200 && response.data && response.data.code === 200) {
        ElMessage.success('申请已撤销')
        // 刷新列表
        loadRecords()
        // 不需要显式调用loadStatistics，因为loadRecords会调用它
      } else {
        ElMessage.error(response.data?.message || '撤销申请失败')
      }
    }).catch(error => {
      console.error('撤销申请失败:', error)
      ElMessage.error('撤销申请失败，请重试')
    })
  })
  .catch(() => {
    // 用户取消操作，不做任何处理
  })
}
</script>

<template>
  <div class="points-history-container">
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>积分历史查询</h3>
          <div class="header-actions">
            <el-button type="primary" @click="exportData">导出数据</el-button>
            <el-button type="primary" @click="refreshData" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-form :model="filterForm" :inline="true" class="filter-form">
        <el-form-item label="学生信息">
          <el-select
            v-model="filterForm.studentId"
            filterable
            remote
            reserve-keyword
            placeholder="请输入学号或姓名搜索"
            :remote-method="remoteStudentSearch"
            :loading="studentSearchLoading"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="item in studentOptions"
              :key="item.id"
              :label="`${item.id} - ${item.name} (${item.className})`"
              :value="item.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ item.name }}</span>
                <span style="font-size: 12px; color: #8492a6">{{ item.id }}</span>
              </div>
              <div style="font-size: 12px; color: #8492a6">{{ item.className }}</div>
            </el-option>
          </el-select>
        </el-form-item>
        
     
        
        <el-form-item label="积分类型">
          <el-select v-model="filterForm.type" placeholder="请选择类型" clearable style="width: 120px">
            <el-option label="加分" value="add"></el-option>
            <el-option label="扣分" value="deduct"></el-option>
          </el-select>
        </el-form-item>
        
      
        
        <el-form-item label="状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 320px"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>积分历史记录</h3>
          <div class="summary-info" v-loading="statsLoading" element-loading-background="rgba(255, 255, 255, 0.5)">
            <el-tag type="success" class="summary-tag" effect="dark">
              总加分: <span class="stat-value">{{ totalAddPoints }}</span>
            </el-tag>
            <el-tag type="danger" class="summary-tag" effect="dark">
              总扣分: <span class="stat-value">{{ totalDeductPoints }}</span>
            </el-tag>
            <el-tag type="info" class="summary-tag" effect="dark">
              净积分: <span class="stat-value">{{ netPoints }}</span>
            </el-tag>
            <el-tag type="warning" class="summary-tag" effect="dark">
              记录数: <span class="stat-value">{{ totalRecords }}</span>
            </el-tag>
          </div>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="applyId" label="记录ID" width="80" sortable />
        <el-table-column prop="studentId" label="学号" width="100" sortable />
        <el-table-column prop="studentName" label="学生姓名" width="100" />
        <el-table-column prop="className" label="班级" width="150" />
        <el-table-column label="积分类型" width="150">
          <template #default="scope">
            {{ scope.row.type === 'add' ? '加分项目' : '扣分项目' }}
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分" width="80" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.type === 'add' ? 'success' : 'danger'">
              {{ scope.row.type === 'add' ? '+' : '-' }}{{ scope.row.points }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="申请时间" width="170" sortable />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button link type="primary" size="small" @click="cancelApplication(scope.row)" v-if="scope.row.status === 'pending'">撤销</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRecords"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" :title="getDetailTitle(currentDetail)" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="学生姓名">{{ currentDetail.studentName }}</el-descriptions-item>
        <el-descriptions-item label="学号">{{ currentDetail.studentId }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ currentDetail.className }}</el-descriptions-item>
        <el-descriptions-item label="积分类型">
          <el-tag :type="currentDetail.type === 'add' ? 'success' : 'danger'">
            {{ currentDetail.type === 'add' ? '加分' : '扣分' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="积分数值">
          <el-tag :type="currentDetail.type === 'add' ? 'success' : 'danger'">
            {{ currentDetail.type === 'add' ? '+' : '-' }}{{ currentDetail.points }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentDetail.status)">{{ getStatusText(currentDetail.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作人ID">{{ currentDetail.applyUserId }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ currentDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="审核时间" v-if="currentDetail.reviewTime">{{ currentDetail.reviewTime }}</el-descriptions-item>
        <el-descriptions-item label="审核人ID" v-if="currentDetail.reviewerId">{{ currentDetail.reviewerId }}</el-descriptions-item>
        <el-descriptions-item label="原因" :span="2">{{ currentDetail.reason }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2" v-if="currentDetail.reviewComment">{{ currentDetail.reviewComment }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="attachments-container" v-if="currentDetail.evidenceImages && currentDetail.evidenceImages.length > 0">
        <h4>证明材料</h4>
        <div class="image-gallery">
          <el-image
            v-for="(url, index) in (currentDetail.processedEvidenceImages || currentDetail.evidenceImages).split(',')"
            :key="index"
            :src="url.trim()"
            :preview-src-list="(currentDetail.processedEvidenceImages || currentDetail.evidenceImages).split(',').map(u => u.trim())"
            :initial-index="index"
            fit="cover"
            class="attachment-image"
            @error="() => handleImageError(url)"
          >
            <template #error>
              <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #909399;">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="cancelApplication(currentDetail)" v-if="currentDetail.status === 'pending'">撤销申请</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.points-history-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.points-history-container::-webkit-scrollbar {
  display: none;
}

.filter-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible; /* 改为visible，避免内容被截断 */
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.filter-card:hover, .table-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.summary-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.summary-tag {
  font-weight: 500;
  padding: 6px 10px;
  margin-right: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.summary-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-weight: 700;
  margin-left: 4px;
  transition: all 0.3s ease;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px; /* 添加底部边距 */
  display: flex;
  justify-content: flex-end;
}

.attachments-container {
  margin-top: 20px;
}

.attachments-container h4 {
  margin-bottom: 10px;
  font-weight: 500;
}

.attachment-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions, .summary-info {
    width: 100%;
    justify-content: space-between;
  }
}
</style>