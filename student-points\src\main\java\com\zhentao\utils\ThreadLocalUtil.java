package com.zhentao.utils;



public class ThreadLocalUtil {
    // 提供ThreadLocal对象
    private static final ThreadLocal THREAD_LOCAL = new ThreadLocal();
    // 获取数据的方法，根据键获取值
    public static <T> T get(){
        return (T) THREAD_LOCAL.get();
    }
    // 存储数据的方法，储存键值对
    public static void set(Object value){
        THREAD_LOCAL.set(value);
    }
    // 删除数据的方法，清除ThreadLocal,防止内存溢出
    public static void remove(){
        THREAD_LOCAL.remove();
    }
}
