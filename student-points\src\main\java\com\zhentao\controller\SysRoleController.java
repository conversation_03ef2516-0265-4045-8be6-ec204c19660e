package com.zhentao.controller;

import com.zhentao.pojo.SysRole;
import com.zhentao.service.SysRoleService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/sysRole")
public class SysRoleController {
    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 获取所有角色列表
     */
    @PostMapping("/findAllRole")
    public Result findAllRole(){
        return sysRoleService.findAllRole();
    }

    /**
     * 根据ID查询角色详情
     */
    @PostMapping("/findRoleById")
    public SysRole findRoleById(@RequestParam("roleId") Integer roleId){
        return sysRoleService.findRoleById(roleId);
    }

    /**
     * 获取所有权限
     */
    @PostMapping("/findAllPermission")
    public Result findAllPermission(){
        return sysRoleService.findAllPermission();
    }

    /**
     * 获取角色权限
     */
    @PostMapping("/findRolePermission")
    public Result findRolePermission(@RequestParam("roleId") Integer roleId){
        return sysRoleService.findRolePermission(roleId);
    }
    
    /**
     * 添加角色
     */
    @PostMapping("/addRole")
    public Result addRole(@RequestBody SysRole sysRole) {
        return sysRoleService.addRole(sysRole);
    }
    
    /**
     * 更新角色
     */
    @PostMapping("/updateRole")
    public Result updateRole(@RequestBody SysRole sysRole) {
        return sysRoleService.updateRole(sysRole);
    }
    
    /**
     * 删除角色
     */
    @PostMapping("/deleteRole")
    public Result deleteRole(@RequestParam("roleId") Integer roleId) {
        return sysRoleService.deleteRole(roleId);
    }
    
    /**
     * 更新角色权限
     */
    @PostMapping("/updateRolePermission")
    public Result updateRolePermission(@RequestBody UpdateRolePermissionRequest request) {
        return sysRoleService.updateRolePermission(request.getRoleId(), request.getMenuIds());
    }
    
    /**
     * 查询角色关联的用户列表
     */
    @PostMapping("/findRoleUsers")
    public Result findRoleUsers(@RequestParam("roleId") Integer roleId) {
        return sysRoleService.findRoleUsers(roleId);
    }
    
    /**
     * 用于接收更新角色权限请求的内部类
     */
    public static class UpdateRolePermissionRequest {
        private Integer roleId;
        private List<Integer> menuIds;
        
        public Integer getRoleId() {
            return roleId;
        }
        
        public void setRoleId(Integer roleId) {
            this.roleId = roleId;
        }
        
        public List<Integer> getMenuIds() {
            return menuIds;
        }
        
        public void setMenuIds(List<Integer> menuIds) {
            this.menuIds = menuIds;
        }
    }
}
