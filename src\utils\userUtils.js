/**
 * User utilities for authentication and user information management
 */
import { useAppStore } from '@/stores/appStore';

// Token key in localStorage
const TOKEN_KEY = 'Authorization';
const USER_INFO_KEY = 'UserInfo';

/**
 * Get JWT token from localStorage
 * @returns {string|null} The JWT token or null if not found
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY);
}

/**
 * Set JWT token in localStorage
 * @param {string} token - The JWT token to store
 */
export function setToken(token) {
  if (token) {
    // Ensure token has Bearer prefix
    if (!token.startsWith('Bearer ')) {
      token = `Bearer ${token}`;
    }
    localStorage.setItem(TOKEN_KEY, token);
  }
}

/**
 * Parse JWT token to get payload
 * @param {string} token - The JWT token to parse
 * @returns {Object|null} The decoded payload or null if invalid
 */
export function parseToken(token) {
  try {
    if (!token) return null;
    
    // Remove 'Bearer ' prefix if present
    if (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }
    
    // Get the payload part of the JWT
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to parse JWT token:', error);
    return null;
  }
}

/**
 * Save user information to localStorage and store
 * @param {Object} userInfo - User information object
 */
export function saveUserInfo(userInfo) {
  if (userInfo) {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
    
    // Update the app store
    const appStore = useAppStore();
    appStore.setUserInfo(userInfo);
  }
}

/**
 * Get current user information from localStorage and store
 * @returns {Object|null} User information or null if not logged in
 */
export function getCurrentUser() {
  try {
    // First check if we have user info in localStorage
    const userInfoStr = localStorage.getItem(USER_INFO_KEY);
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr);
      return userInfo;
    }
    
    // If not, try to get from token
    const token = getToken();
    if (!token) return null;
    
    // For demo purposes, return a default user if we can't parse the token
    const payload = parseToken(token);
    if (!payload) {
      return {
        name: '管理员',
        roles: ['admin'],
        avatar: '',
        permissions: []
      };
    }
    
    return {
      name: payload.name || '管理员',
      roles: payload.roles || ['admin'],
      avatar: payload.avatar || '',
      permissions: payload.permissions || []
    };
  } catch (error) {
    console.error('Failed to get user information:', error);
    return null;
  }
}

/**
 * Check if user is logged in
 * @returns {boolean} True if the user is logged in, false otherwise
 */
export function isLoggedIn() {
  return !!getToken();
}

/**
 * Clear all user data during logout
 */
export function clearUserData() {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USER_INFO_KEY);
  
  // Clear app store
  const appStore = useAppStore();
  appStore.clearCache();
  appStore.setUserInfo({
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  });
}

/**
 * Check if token is expired
 * @returns {boolean} True if token is expired or invalid
 */
export function isTokenExpired() {
  try {
    const token = getToken();
    if (!token) return true;
    
    const payload = parseToken(token);
    if (!payload || !payload.exp) return true;
    
    // Check if token is expired (exp is in seconds)
    const now = Math.floor(Date.now() / 1000);
    return payload.exp < now;
  } catch (error) {
    console.error('Failed to check token expiration:', error);
    return true;
  }
} 