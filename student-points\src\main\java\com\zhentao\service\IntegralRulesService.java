package com.zhentao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.pojo.IntegralRules;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface IntegralRulesService extends IService<IntegralRules> {

    Page<IntegralRules> findAllJia(@RequestBody IntegralRules integralRules);

    Page<IntegralRules> findAllJian(@RequestBody IntegralRules integralRules);
}
