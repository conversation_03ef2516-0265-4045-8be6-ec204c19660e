<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

onMounted(() => {
  // 重定向到ClassManagement.vue
  router.replace('/student/class-management');
});
</script>

<template>
  <div class="redirect-container">
    <p>正在重定向到班级管理页面...</p>
  </div>
</template>

<style scoped>
.redirect-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #606266;
}
</style>