<template>
  <div class="class-management-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="class-form-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>{{ isEdit ? '编辑班级' : '添加班级' }}</h3>
            </div>
          </template>
          
          <el-form :model="classForm" :rules="classRules" ref="classFormRef" label-width="100px">
            <el-form-item label="班级名称" prop="className">
              <el-input v-model="classForm.className" placeholder="请输入班级名称"></el-input>
            </el-form-item>
            
            <el-form-item label="所属阶段" prop="stageId">
              <el-select v-model="classForm.stageId" placeholder="请选择专业" style="width: 100%">
                <el-option 
                  v-for="item in stageOptions" 
                  :key="item.stageId" 
                  :label="`${item.stageName}${item.courseName ? ' - ' + item.courseName : ''}`" 
                  :value="item.stageId">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="教室号" prop="classroom">
              <el-input v-model="classForm.classroom" placeholder="请输入教室号"></el-input>
            </el-form-item>
            
            <el-form-item label="讲师" prop="teacherId">
              <el-select v-model="classForm.teacherId" placeholder="请选择讲师" style="width: 100%">
                <el-option 
                  v-for="teacher in teacherOptions" 
                  :key="teacher.userId" 
                  :label="teacher.realName" 
                  :value="teacher.userId">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="导员" prop="counselorId">
              <el-select v-model="classForm.counselorId" placeholder="请选择导员" style="width: 100%">
                <el-option 
                  v-for="counselor in counselorOptions" 
                  :key="counselor.userId" 
                  :label="counselor.realName" 
                  :value="counselor.userId">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="阶段主任" prop="directorId">
              <el-select v-model="classForm.directorId" placeholder="请选择阶段主任" style="width: 100%">
                <el-option 
                  v-for="director in directorOptions" 
                  :key="director.userId" 
                  :label="director.realName" 
                  :value="director.userId">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="开班日期" prop="startDate">
              <el-date-picker
                v-model="classForm.startDate"
                type="date"
                placeholder="选择开班日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
            
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="classForm.endDate"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              ></el-date-picker>
            </el-form-item>
            
            <el-form-item label="状态" prop="status">
              <el-select v-model="classForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option :value="0" label="正常"></el-option>
                <el-option :value="1" label="结束"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="备注" prop="remark">
              <el-input v-model="classForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="submitClassForm" :loading="submitting">{{ isEdit ? '更新' : '添加' }}</el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-button v-if="isEdit" @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card class="class-list-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>班级列表</h3>
              <div class="header-actions">
                <el-input
                  v-model="searchText"
                  placeholder="搜索班级名称"
                  style="width: 200px"
                  clearable
                  @clear="handleSearch"
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click="refreshTable" :icon="Refresh">刷新</el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
            <el-table-column type="index" width="60" label="序号" />
            <el-table-column prop="className" label="班级名称" min-width="100" />
            <el-table-column prop="remark" label="所属专业" min-width="120" />
            <el-table-column prop="classroom" label="教室号" width="100" />
            <el-table-column label="讲师" width="100">
              <template #default="scope">
                <el-tooltip :content="`ID: ${scope.row.teacherId || '未设置'}`" placement="top" effect="light">
                <span>{{ getTeacherName(scope.row.teacherId) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="导员" width="100">
              <template #default="scope">
                <el-tooltip :content="`ID: ${scope.row.counselorId || '未设置'}`" placement="top" effect="light">
                <span>{{ getCounselorName(scope.row.counselorId) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="开班日期" width="100" sortable>
              <template #default="scope">
                <el-tooltip :content="scope.row.startDate || '未设置'" placement="top" effect="light">
                  <span>{{ formatDate(scope.row.startDate) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="结束日期" width="100" sortable>
              <template #default="scope">
                <el-tooltip :content="scope.row.endDate || '未设置'" placement="top" effect="light">
                  <span>{{ formatDate(scope.row.endDate) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="班级人数" width="90" sortable>
              <template #default="scope">
                <el-tag :type="getCountTagType(scope.row.students ? scope.row.students.length : 0)">
                  {{ scope.row.students ? scope.row.students.length : 0 }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 0 ? 'success' : 'info'">
                  {{ scope.row.status === 0 ? '正常' : '结束' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220" fixed="right">
              <template #default="scope">
                <el-button link type="primary" size="small" @click="viewStudents(scope.row)">查看学生</el-button>
                <el-button link type="primary" size="small" @click="editClass(scope.row)">编辑</el-button>
                <el-button link type="danger" size="small" @click="handleDeleteClass(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[5, 10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 班级学生列表对话框 -->
    <el-dialog v-model="studentsDialogVisible" :title="`${currentClass.className || ''}学生列表`" width="80%">
      <el-table :data="classStudents" style="width: 100%" border stripe v-loading="studentsLoading">
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="studentNo" label="学号" width="120" />
        <el-table-column prop="realName" label="姓名" width="100" />
        <el-table-column label="性别" width="60">
          <template #default="scope">
            {{ scope.row.gender === 1 ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分" width="80">
          <template #default="scope">
            <span :class="getPointsClass(scope.row.points)">{{ scope.row.points }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="getStudentStatusType(scope.row.status)">
              {{ getStudentStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="class-statistics">
        <el-descriptions title="班级统计" :column="4" border>
          <el-descriptions-item label="班级人数">{{ classStatistics.totalStudents || 0 }}</el-descriptions-item>
          <el-descriptions-item label="平均积分">{{ classStatistics.averagePoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最高积分">{{ classStatistics.maxPoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最低积分">{{ classStatistics.minPoints || 0 }}</el-descriptions-item>
          <el-descriptions-item label="男生人数">{{ classStatistics.maleCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="女生人数">{{ classStatistics.femaleCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="专业名称">{{ classStatistics.stageName || '未知' }}</el-descriptions-item>
          <el-descriptions-item label="班级状态">{{ currentClass.status === 0 ? '正常' : '结束' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="studentsDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleExportClassStudents">导出学生名单</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search } from '@element-plus/icons-vue'
import { getClassList, addClass, updateClass, deleteClass, getClassStudents, getClassStatistics, exportClassStudents, getStageList } from '@/api/system/class'
import { getUsersByType } from '@/api/system/user'
import { useRouter } from 'vue-router'
import { format } from 'date-fns'

const router = useRouter()

// 专业选项
const stageOptions = ref([])
const teacherOptions = ref([])
const counselorOptions = ref([])
const directorOptions = ref([])

// 表单数据
const classFormRef = ref(null)
const isEdit = ref(false)
const submitting = ref(false)
const classForm = reactive({
  classId: null,
  className: '',
  stageId: null,
  classroom: '',
  teacherId: null,
  counselorId: null,
  directorId: null,
  startDate: '',
  endDate: '',
  status: 0,
  remark: ''
})

// 表单验证规则
const classRules = {
  className: [
    { required: true, message: '请输入班级名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  stageId: [
    { required: true, message: '请选择所属专业', trigger: 'change' }
  ],
  classroom: [
    { required: true, message: '请输入教室号', trigger: 'blur' }
  ],
  teacherId: [
    { required: true, message: '请选择讲师', trigger: 'change' }
  ],
  counselorId: [
    { required: true, message: '请选择导员', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开班日期', trigger: 'change' }
  ]
}

// 班级列表数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索
const searchText = ref('')

// 班级学生对话框
const studentsDialogVisible = ref(false)
const studentsLoading = ref(false)
const classStudents = ref([])
const currentClass = reactive({})
const classStatistics = reactive({})

// 获取专业列表
const fetchStageOptions = async () => {
  try {
    const res = await getStageList()
    // 修复：处理不同的响应格式
    if (res.data && res.data.code === 200) {
      // 如果是标准Result格式
      stageOptions.value = res.data.data || []
    } else {
      // 兼容旧格式
    stageOptions.value = res.data.result || []
    }
  } catch (error) {
    console.error('获取专业列表失败:', error)
    ElMessage.error('获取专业列表失败')
  }
}

// 获取用户列表（讲师、导员、主任）
const fetchUserOptions = async () => {
  try {
    // 获取讲师（用户类型1）
    const teachersRes = await getUsersByType(1)
    // 修复：处理不同的响应格式
    if (teachersRes.data && teachersRes.data.code === 200) {
      teacherOptions.value = teachersRes.data.data || []
    } else {
    teacherOptions.value = teachersRes.data.result || []
    }
    
    // 获取导员（用户类型2）
    const counselorsRes = await getUsersByType(2)
    if (counselorsRes.data && counselorsRes.data.code === 200) {
      counselorOptions.value = counselorsRes.data.data || []
    } else {
    counselorOptions.value = counselorsRes.data.result || []
    }
    
    // 获取阶段主任（用户类型3）
    const directorsRes = await getUsersByType(3)
    if (directorsRes.data && directorsRes.data.code === 200) {
      directorOptions.value = directorsRes.data.data || []
    } else {
    directorOptions.value = directorsRes.data.result || []
    }
    
    console.log('讲师列表:', teacherOptions.value)
    console.log('导员列表:', counselorOptions.value)
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 获取讲师名称
const getTeacherName = (teacherId) => {
  if (!teacherId) return '-'
  const teacher = teacherOptions.value.find(item => item.userId === teacherId)
  if (teacher) {
    return teacher.realName || teacher.username || '-'
  }
  return '-'
}

// 获取导员名称
const getCounselorName = (counselorId) => {
  if (!counselorId) return '-'
  const counselor = counselorOptions.value.find(item => item.userId === counselorId)
  if (counselor) {
    return counselor.realName || counselor.username || '-'
  }
  return '-'
}

// 获取班级列表
const fetchClassList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      className: searchText.value
    }
    
    const res = await getClassList(params)
    console.log('班级列表数据:', res.data)
    
    if (res.data && res.data.records) {
    tableData.value = res.data.records || []
    total.value = res.data.total || 0
      
      // 打印一些调试信息，查看班级数据
      if (tableData.value.length > 0) {
        const firstClass = tableData.value[0]
        console.log('第一个班级数据:', firstClass)
        console.log('讲师ID:', firstClass.teacherId)
        console.log('导员ID:', firstClass.counselorId)
        console.log('开始日期:', firstClass.startDate)
        console.log('结束日期:', firstClass.endDate)
      }
    } else if (res.data && res.data.code === 200 && res.data.data) {
      // 处理标准Result格式
      const data = res.data.data
      tableData.value = data.records || []
      total.value = data.total || 0
    } else {
      tableData.value = []
      total.value = 0
      console.warn('获取班级列表返回的数据格式不符合预期:', res.data)
    }
  } catch (error) {
    console.error('获取班级列表失败:', error)
    ElMessage.error('获取班级列表失败')
  } finally {
    loading.value = false
  }
}

// 添加/更新班级
const submitClassForm = async () => {
  if (!classFormRef.value) return
  
  await classFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请正确填写表单信息')
      return
    }
    
    submitting.value = true
    try {
      const formData = { ...classForm }
      
      console.log('提交前的表单数据:', formData)
      
      // 确保所有必要字段都存在
      if (!formData.className) {
        throw new Error('班级名称不能为空')
      }
      
      if (!formData.stageId) {
        throw new Error('所属专业不能为空')
      }
      
      if (!formData.teacherId) {
        throw new Error('讲师不能为空')
      }
      
      if (isEdit.value) {
        // 更新班级
        try {
          const updateRes = await updateClass(formData)
          console.log('更新班级响应:', updateRes)
          
          if (updateRes.data && updateRes.data.code === 200) {
            ElMessage.success(updateRes.data.message || '更新班级成功')
            // 重置表单
            resetForm()
            // 刷新班级列表
            fetchClassList()
          } else {
            throw new Error(updateRes.data?.message || '更新失败，请检查数据格式')
          }
        } catch (error) {
          console.error('更新班级失败:', error)
          ElMessage.error(error.message || '更新班级失败')
        }
      } else {
        // 添加班级
        try {
          const addRes = await addClass(formData)
          console.log('添加班级响应:', addRes)
          
          if (addRes.data && addRes.data.code === 200) {
            ElMessage.success(addRes.data.message || '添加班级成功')
      // 重置表单
      resetForm()
      // 刷新班级列表
      fetchClassList()
          } else {
            throw new Error(addRes.data?.message || '添加失败，请检查数据格式')
          }
        } catch (error) {
          console.error('添加班级失败:', error)
          ElMessage.error(error.message || '添加班级失败')
        }
      }
      } catch (error) {
      console.error('表单提交失败:', error)
      ElMessage.error(error.message || '操作失败')
      } finally {
        submitting.value = false
      }
  })
}

// 编辑班级
const editClass = (row) => {
  isEdit.value = true
  
  console.log('编辑班级数据:', row)
  
  // 复制班级数据到表单
  Object.keys(classForm).forEach(key => {
    if (key === 'startDate' || key === 'endDate') {
      // 日期特殊处理
      if (row[key]) {
        try {
          // 统一转换为YYYY-MM-DD格式
          const date = new Date(row[key])
          if (!isNaN(date.getTime())) {
            classForm[key] = format(date, 'yyyy-MM-dd')
          } else {
            classForm[key] = ''
          }
        } catch (e) {
          console.error(`处理${key}出错:`, e)
          classForm[key] = ''
        }
      } else {
        classForm[key] = ''
      }
    } else {
      classForm[key] = row[key]
    }
  })
  
  console.log('表单数据:', classForm)
  
  // 滚动到表单顶部
  setTimeout(() => {
    const formCard = document.querySelector('.class-form-card')
    if (formCard) {
      formCard.scrollIntoView({ behavior: 'smooth' })
    }
  }, 100)
}

// 取消编辑
const cancelEdit = () => {
  isEdit.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (classFormRef.value) {
    classFormRef.value.resetFields()
  }
  
  isEdit.value = false
  
  // 清空表单数据
  Object.keys(classForm).forEach(key => {
    if (key === 'status') {
      classForm[key] = 0
    } else {
      classForm[key] = null
    }
  })
  
  classForm.className = ''
  classForm.classroom = ''
  classForm.startDate = ''
  classForm.endDate = ''
  classForm.remark = ''
}

// 删除班级
const handleDeleteClass = (row) => {
  ElMessageBox.confirm(`确定要删除班级"${row.className}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteClass(row.classId)
      ElMessage.success('删除班级成功')
      fetchClassList()
    } catch (error) {
      console.error('删除班级失败:', error)
      ElMessage.error('删除班级失败')
    }
  }).catch(() => {
    // 用户取消删除，不做处理
  })
}

// 查看班级学生
const viewStudents = async (row) => {
  currentClass.classId = row.classId
  currentClass.className = row.className
  currentClass.status = row.status
  
  studentsDialogVisible.value = true
  studentsLoading.value = true
  
  try {
    // 获取班级学生
    const studentsRes = await getClassStudents(row.classId)
    console.log('获取到的学生数据:', studentsRes)
    
    // 修复：正确处理后端返回的数据格式
    // 后端返回的是Result对象，学生数据在data字段中
    if (studentsRes.data && studentsRes.data.code === 200) {
      // 成功获取数据
      classStudents.value = studentsRes.data.data || []
    } else {
      console.warn('获取到的学生数据格式不符合预期:', studentsRes)
      classStudents.value = []
      ElMessage.warning('获取班级学生数据格式异常')
    }
    
    // 获取班级统计数据
    const statsRes = await getClassStatistics(row.classId)
    if (statsRes.data && statsRes.data.code === 200) {
      Object.assign(classStatistics, statsRes.data.data || {})
    } else {
      console.warn('获取到的班级统计数据格式异常:', statsRes)
      // 保持默认的空对象
    }
  } catch (error) {
    console.error('获取班级学生数据失败:', error)
    ElMessage.error('获取班级学生数据失败')
  } finally {
    studentsLoading.value = false
  }
}

// 导出班级学生名单
const handleExportClassStudents = async () => {
  try {
    const response = await exportClassStudents(currentClass.classId)
    
    // 创建Blob对象
    const blob = new Blob([response.data], { type: response.headers['content-type'] })
    
    // 创建URL
    const url = window.URL.createObjectURL(blob)
    
    // 创建下载链接并触发点击
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${currentClass.className}学生名单.xlsx`
    document.body.appendChild(a)
    a.click()
    
    // 清理
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出学生名单失败:', error)
    ElMessage.error('导出学生名单失败')
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchClassList()
}

// 刷新表格
const refreshTable = () => {
  fetchClassList()
}

// 改变页码大小
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchClassList()
}

// 改变当前页
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchClassList()
}

// 获取班级人数标签类型
const getCountTagType = (count) => {
  if (count > 30) return 'success'
  if (count > 15) return 'warning'
  if (count > 0) return 'info'
  return 'danger'
}

// 获取积分样式
const getPointsClass = (points) => {
  if (!points) return ''
  if (points > 120) return 'points-high'
  if (points < 80) return 'points-low'
  return 'points-normal'
}

// 获取学生状态标签类型
const getStudentStatusType = (status) => {
  if (status === 0) return 'success'
  if (status === 1) return 'warning'
  return 'danger'
}

// 获取学生状态文本
const getStudentStatusText = (status) => {
  if (status === 0) return '正常'
  if (status === 1) return '休学'
  if (status === 2) return '退学'
  return '未知'
}

// 日期格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    // 处理日期格式
    let date
    if (typeof dateString === 'string' && dateString.includes('T')) {
      // ISO格式日期
      date = new Date(dateString)
    } else if (typeof dateString === 'number') {
      // 时间戳
      date = new Date(dateString)
    } else if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // YYYY-MM-DD格式
      const [year, month, day] = dateString.split('-').map(Number)
      date = new Date(year, month - 1, day)
    } else {
      // 其他格式尝试直接解析
      date = new Date(dateString)
    }
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期:', dateString)
      return '-'
    }
    
    return format(date, 'yyyy-MM-dd')
  } catch (e) {
    console.error('格式化日期出错:', e, dateString)
    return '-'
  }
}

// 初始化
onMounted(async () => {
  try {
    // 先获取用户选项，以便在获取班级列表时能正确显示讲师和导员
    await fetchUserOptions()
    await fetchStageOptions()
    await fetchClassList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请刷新页面重试')
  }
})
</script>

<style scoped>
.class-management-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.class-statistics {
  margin-top: 20px;
}

.class-form-card, .class-list-card {
  height: 100%;
}

.points-high {
  color: #67c23a;
  font-weight: bold;
}

.points-low {
  color: #f56c6c;
  font-weight: bold;
}

.points-normal {
  color: #409eff;
}
</style> 