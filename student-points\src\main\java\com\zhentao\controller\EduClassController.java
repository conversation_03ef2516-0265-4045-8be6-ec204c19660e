package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStage;
import com.zhentao.pojo.EduStudent;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStageService;
import com.zhentao.service.EduStudentService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 班级表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/edu-class")
public class EduClassController {

    @Autowired
    private EduClassService eduClassService;
    
    @Autowired
    private EduStudentService eduStudentService;
    
    @Autowired
    private EduStageService eduStageService;
    
    @PostMapping("/findPage")
    public Page<EduClass> findPage(@RequestBody EduClass eduClass){
        try {
        Page<EduClass> page = new Page<>(eduClass.getPageNum(), eduClass.getPageSize());
        LambdaQueryWrapper<EduClass> wrapper = new LambdaQueryWrapper<>(); // 根据班级名称模糊查询
            wrapper.like(StringUtils.isNotBlank(eduClass.getClassName()), EduClass::getClassName, eduClass.getClassName());
            wrapper.eq(EduClass::getDelFlag,0);
            wrapper.orderByDesc(EduClass::getCreateTime);
            
        Page<EduClass> page1 = eduClassService.page(page, wrapper);
            
            // 获取每个班级的学生数量和专业名称
        for (EduClass record : page1.getRecords()) {
                // 获取学生列表
                LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
                studentWrapper.eq(EduStudent::getClassId, record.getClassId());
                List<EduStudent> students = eduStudentService.list(studentWrapper);
                record.setStudents(students);
                
                // 获取专业名称
                if (record.getStageId() != null) {
                    EduStage stage = eduStageService.getById(record.getStageId());
                    if (stage != null) {
                        record.setRemark(stage.getStageName()); // 暂时使用remark字段存储专业名称
                    }
                }
            }
            
            return page1;
        } catch (Exception e) {
            e.printStackTrace();
            return new Page<>();
        }
    }
    
    @PostMapping("/addClass")
    public Result addClass(@RequestBody EduClass eduClass) {
        try {
            // 设置创建时间和创建人
            eduClass.setCreateTime(new Date());
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                eduClass.setCreateBy(currentUserId);
            }
            
            // 设置默认值
            if (eduClass.getStatus() == null) {
                eduClass.setStatus(0); // 默认正常状态
            }
            eduClass.setDelFlag(0); // 默认未删除
            
            boolean save = eduClassService.save(eduClass);
            if (save) {
                return Result.OK(eduClass);
            } else {
                return Result.ERROR("添加失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("添加失败：" + e.getMessage());
        }
    }
    
    @PostMapping("/updateClass")
    public Result updateClass(@RequestBody EduClass eduClass) {
        try {
            System.out.println("接收到的班级数据: " + eduClass);
            
            // 设置更新时间
        eduClass.setUpdateTime(new Date());
            
            // 设置更新人
            Integer currentUserId = UserContext.getCurrentUserId();
            if (currentUserId != null) {
                eduClass.setUpdateBy(currentUserId);
            }
            
            // 检查必要字段
            if (eduClass.getClassId() == null) {
                return Result.ERROR("班级ID不能为空");
            }
            
            // 查询原班级数据
            EduClass originalClass = eduClassService.getById(eduClass.getClassId());
            if (originalClass == null) {
                return Result.ERROR("班级不存在");
            }
            
            // 保留原班级的学生列表（如果有）
            if (originalClass.getStudents() != null) {
                eduClass.setStudents(originalClass.getStudents());
            }
            
            // 更新班级
        boolean update = eduClassService.updateById(eduClass);
            if (update) {
                return Result.OK("修改成功");
            } else {
                return Result.ERROR("修改失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("修改失败：" + e.getMessage());
        }
    }
    
    @RequestMapping("delClass")
    public String delClass(@RequestParam("id") Integer id) {
        try {
            // 检查班级是否有学生
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, id);
            long count = eduStudentService.count(wrapper);
            
            if (count > 0) {
                return "删除失败：该班级下还有" + count + "名学生，请先移除学生";
            }
            
        boolean del = eduClassService.removeById(id);
            return del ? "删除成功" : "删除失败";
        } catch (Exception e) {
            e.printStackTrace();
            return "删除失败：" + e.getMessage();
        }
    }
    
    @GetMapping("/findByStageId")
    public Result findByStageId(@RequestParam("stageId") Integer stageId) {
        try {
            LambdaQueryWrapper<EduClass> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduClass::getStageId, stageId);
            wrapper.eq(EduClass::getStatus, 0); // 只查询正常状态的班级
            wrapper.orderByAsc(EduClass::getClassName);
            
            List<EduClass> list = eduClassService.list(wrapper);
            return Result.OK(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级列表失败：" + e.getMessage());
        }
    }
    
    @GetMapping("/getClassStatistics")
    public Result getClassStatistics(@RequestParam("classId") Integer classId) {
        try {
            // 获取班级信息
            EduClass eduClass = eduClassService.getById(classId);
            if (eduClass == null) {
                return Result.ERROR("班级不存在");
            }
            
            // 获取班级学生
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, classId);
            List<EduStudent> students = eduStudentService.list(wrapper);
            
            // 统计数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalStudents", students.size());
            
            int maleCount = 0;
            int femaleCount = 0;
            int totalPoints = 0;
            int maxPoints = 0;
            int minPoints = Integer.MAX_VALUE;
            
            for (EduStudent student : students) {
                // 性别统计
                if (student.getGender() != null) {
                    if (student.getGender() == 1) {
                        maleCount++;
                    } else {
                        femaleCount++;
                    }
                }
                
                // 积分统计
                Integer points = student.getPoints();
                if (points != null) {
                    totalPoints += points;
                    maxPoints = Math.max(maxPoints, points);
                    minPoints = Math.min(minPoints, points);
                }
            }
            
            statistics.put("maleCount", maleCount);
            statistics.put("femaleCount", femaleCount);
            
            if (!students.isEmpty()) {
                statistics.put("averagePoints", students.isEmpty() ? 0 : totalPoints / students.size());
                statistics.put("maxPoints", students.isEmpty() ? 0 : maxPoints);
                statistics.put("minPoints", students.isEmpty() || minPoints == Integer.MAX_VALUE ? 0 : minPoints);
            } else {
                statistics.put("averagePoints", 0);
                statistics.put("maxPoints", 0);
                statistics.put("minPoints", 0);
            }
            
            // 获取专业信息
            if (eduClass.getStageId() != null) {
                EduStage stage = eduStageService.getById(eduClass.getStageId());
                if (stage != null) {
                    statistics.put("stageName", stage.getStageName());
                }
            }
            
            return Result.OK(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/exportStudents")
    public void exportStudents(@RequestParam("classId") Integer classId, HttpServletResponse response) {
        XSSFWorkbook workbook = null;
        try {
            // 获取班级信息
            EduClass eduClass = eduClassService.getById(classId);
            if (eduClass == null) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"班级不存在\"}");
                return;
            }
            
            // 获取班级学生
            LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(EduStudent::getClassId, classId);
            wrapper.orderByAsc(EduStudent::getStudentNo);
            List<EduStudent> students = eduStudentService.list(wrapper);
            
            // 获取专业信息
            String stageName = "未知";
            if (eduClass.getStageId() != null) {
                EduStage stage = eduStageService.getById(eduClass.getStageId());
                if (stage != null) {
                    stageName = stage.getStageName();
                }
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 添加日期到文件名
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String dateStr = sdf.format(new Date());
            String fileName = URLEncoder.encode("班级学生_" + eduClass.getClassName() + "_" + dateStr, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            // 防止浏览器缓存
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            
            // 创建Excel工作簿
            workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("学生名单");
            
            // 创建班级信息行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("班级：" + eduClass.getClassName() + "    专业：" + stageName + "    学生人数：" + students.size());
            
            // 合并单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));
            
            // 设置列宽
            sheet.setColumnWidth(0, 15 * 256); // 学号
            sheet.setColumnWidth(1, 10 * 256); // 姓名
            sheet.setColumnWidth(2, 6 * 256);  // 性别
            sheet.setColumnWidth(3, 8 * 256);  // 积分
            sheet.setColumnWidth(4, 15 * 256); // 联系电话
            sheet.setColumnWidth(5, 25 * 256); // 邮箱
            sheet.setColumnWidth(6, 10 * 256); // 状态
            sheet.setColumnWidth(7, 30 * 256); // 备注
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建内容样式
            CellStyle contentStyle = workbook.createCellStyle();
            contentStyle.setAlignment(HorizontalAlignment.CENTER);
            contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentStyle.setBorderTop(BorderStyle.THIN);
            contentStyle.setBorderRight(BorderStyle.THIN);
            contentStyle.setBorderBottom(BorderStyle.THIN);
            contentStyle.setBorderLeft(BorderStyle.THIN);
            
            // 创建表头
            Row headerRow = sheet.createRow(1);
            headerRow.setHeight((short)(400));
            
            String[] headers = {"学号", "姓名", "性别", "积分", "联系电话", "邮箱", "状态", "备注"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 填充数据
            if (students.isEmpty()) {
                Row emptyRow = sheet.createRow(2);
                Cell emptyCell = emptyRow.createCell(0);
                emptyCell.setCellValue("没有学生数据");
                emptyCell.setCellStyle(contentStyle);
                sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 7));
            } else {
                int rowNum = 2;
                for (EduStudent student : students) {
                    Row row = sheet.createRow(rowNum++);
                    
                    createCell(row, 0, student.getStudentNo(), contentStyle);
                    createCell(row, 1, student.getRealName(), contentStyle);
                    createCell(row, 2, student.getGender() == null ? "未知" : (student.getGender() == 1 ? "男" : "女"), contentStyle);
                    createCell(row, 3, student.getPoints() == null ? "0" : student.getPoints().toString(), contentStyle);
                    createCell(row, 4, student.getPhone(), contentStyle);
                    createCell(row, 5, student.getEmail(), contentStyle);
                    
                    String status = "未知";
                    if (student.getStatus() != null) {
                        switch (student.getStatus()) {
                            case 0:
                                status = "正常";
                                break;
                            case 1:
                                status = "休学";
                                break;
                            case 2:
                                status = "退学";
                                break;
                        }
                    }
                    createCell(row, 6, status, contentStyle);
                    createCell(row, 7, student.getRemark(), contentStyle);
                }
            }
            
            // 输出到响应流
            workbook.write(response.getOutputStream());
            System.out.println("导出班级学生数据完成");
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("导出班级学生数据失败: " + e.getMessage());
            try {
                // 发生异常时返回错误信息
                response.reset(); // 重置响应
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.setStatus(500); // HTTP 500 Internal Server Error
                response.getWriter().write("{\"code\":500,\"message\":\"导出失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            // 关闭工作簿
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 创建单元格并设置值和样式
     */
    private void createCell(Row row, int column, String value, CellStyle style) {
        Cell cell = row.createCell(column);
        cell.setCellValue(value == null ? "" : value);
        cell.setCellStyle(style);
    }
    /**
     * 查询所有班级名称
     */
    @PostMapping("/findAllClassNames")
    public Result findAllClassNames() {
        return eduClassService.findAllClassName();
    }

    //-----------------------------------------虚线一下全都是cmy写的--------------------------------------------------------
    @PostMapping("/queryTopThreeClass")
    public Result queryTopThreeClass() {
        return eduClassService.queryTopThreeClass();
    }
}
