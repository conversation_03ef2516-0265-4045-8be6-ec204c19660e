server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************
    username: feidaowudi
    password: feidaowudi_520
  web:
    resources:
      static-locations: file:///D:/javaGallery/imgs
mybatis-plus:
  type-aliases-package: com.zhentao.pojo
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:mapper/*.xml
minio:
  endpoint: http://182.254.244.209:9000/
  accessKey: minioadmin
  secretKey: minioadmin
  bucket: jifen