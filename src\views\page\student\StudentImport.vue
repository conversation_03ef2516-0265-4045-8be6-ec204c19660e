<script setup>
import { ref, onMounted } from 'vue'
import { getOldStuList } from '@/api/system/oldStu.js'

const tableData = ref([])
const loading = ref(false)
const columns = [
  { prop: 'oldId', label: 'ID', minWidth: 80 },
  { prop: 'oldstuNo', label: '学号', minWidth: 100 },
  { prop: 'stuName', label: '学生姓名', minWidth: 100 },
  { prop: 'gender', label: '性别', minWidth: 60 },
  { prop: 'phone', label: '电话', minWidth: 100 },
  { prop: 'email', label: '邮箱', minWidth: 120 },
  { prop: 'remark', label: '介绍', minWidth: 120 },
  { prop: 'className', label: '班级', minWidth: 100 },
  { prop: 'points', label: '积分', minWidth: 60 },
  // status 列不在 columns 里渲染
  { prop: 'createTime', label: '创建时间', minWidth: 140 }
]

const ruleForm = ref({
  pageNum:1,
  pageSize:10
})
const total = ref(0)

const fetchData = async () => {
  loading.value = true
  getOldStuList(ruleForm.value).then(res => {
    if (res.status == 200) {
      tableData.value = res.data.records
      total.value = res.data.total
      loading.value = false
    }
  })
}

onMounted(fetchData)

function handleCurrentChange(val) {
  ruleForm.value.pageNum = val
  fetchData()
}
function handleSizeChange(val) {
  ruleForm.value.pageSize = val
  ruleForm.value.pageNum = 1
  fetchData()
}
</script>

<template>
  <div class="student-import-view">
    <el-card>
      <template #header>
        <span>学生信息表（只读）</span>
      </template>
      <el-table :data="tableData" v-loading="loading" border stripe style="width: 100%">
        <el-table-column
          v-for="col in columns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :min-width="col.minWidth"
          show-overflow-tooltip
        />
        <el-table-column
          prop="status"
          label="状态"
          :min-width="80"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.status === 0" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === 1" type="warning">休学</el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="danger">退学</el-tag>
            <el-tag v-else>未知</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 16px 0; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="ruleForm.pageSize"
          :current-page="ruleForm.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.student-import-view {
  padding: 24px;
}
</style>