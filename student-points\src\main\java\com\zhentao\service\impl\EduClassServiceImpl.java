package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.dto.system.system.EduClassDto;
import com.zhentao.mapper.EduClassMapper;
import com.zhentao.mapper.EduStudentMapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.service.EduClassService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 班级表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class EduClassServiceImpl extends ServiceImpl<EduClassMapper, EduClass> implements EduClassService {
    @Autowired
    private EduClassMapper eduClassMapper;

    @Autowired
    private EduStudentMapper eduStudentMapper;
    @Override
    public Result queryTopThreeClass() {
        //查询所有班级
        List<EduClass> eduClasses = eduClassMapper.selectList(null);
        // 用于存储每个班级的平均积分等信息
        List<EduClassDto> eduClassDtosList=new ArrayList<>();
        for (EduClass eduClass : eduClasses) {
            Integer classId = eduClass.getClassId();
            QueryWrapper<EduStudent> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("class_id",classId);
            List<EduStudent> eduStudents = eduStudentMapper.selectList(queryWrapper);
            Long countStudent = eduStudentMapper.selectCount(queryWrapper);
            if (countStudent==0){
                //如果班级没有学生，平均分为0，可根据实际需求处理
                continue;
            }
            Integer countPoints=0;
            for (EduStudent eduStudent : eduStudents) {
                if (eduStudent!=null){
                    countPoints+=eduStudent.getPoints();
                }

            }
            //计算平均积分
            Integer endPoints=countPoints/countStudent.intValue();
            //用班级总结分数处于班级人数
            EduClassDto eduClassDto=new EduClassDto();
            eduClassDto.setClassName(eduClass.getClassName());
            eduClassDto.setPoints(endPoints);
            eduClassDtosList.add(eduClassDto);
        }
        //按照平均积分降序排序，取前三名
        eduClassDtosList.sort((a,b)->b.getPoints()-a.getPoints());
        List<EduClassDto> collect = eduClassDtosList.stream().limit(3).collect(Collectors.toList());
        return Result.OK(collect);
    }
    @Override
    public Result findAllClassName() {
        List<EduClass> eduClasses = eduClassMapper.selectList(null);
        List<String> className=new ArrayList<>();
        for (EduClass eduClass : eduClasses) {
            className.add(eduClass.getClassName());
        }
        return Result.OK(className);
    }
}
