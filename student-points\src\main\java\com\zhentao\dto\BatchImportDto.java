package com.zhentao.dto;

import lombok.Data;
import java.util.List;

/**
 * 批量导入DTO
 * <AUTHOR>
 */
@Data
public class BatchImportDto {
    
    /**
     * 班级列表
     */
    private List<ClassImportDto> classes;
    
    /**
     * 班级导入DTO
     */
    @Data
    public static class ClassImportDto {
        /**
         * 班级名称
         */
        private String className;
        
        /**
         * 辅导员姓名
         */
        private String counselor;
        
        /**
         * 讲师姓名
         */
        private String teacher;
        
        /**
         * 教室号
         */
        private String classroom;
        
        /**
         * 所属月度/阶段名称
         */
        private String stageName;
        
        /**
         * 课程名称
         */
        private String courseName;
        
        /**
         * 学生列表
         */
        private List<StudentImportDto> students;
    }
    
    /**
     * 学生导入DTO
     */
    @Data
    public static class StudentImportDto {
        /**
         * 学号
         */
        private String studentNo;
        
        /**
         * 学生姓名
         */
        private String realName;
    }
}
