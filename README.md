# Student-Points-Vue

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

### Lint and Fix Files

```sh
npm run lint
```

### Format Files with Prettier

```sh
npm run format
```

## Linting Guidelines

- Avoid using console.log statements in production code
- Use consistent indentation (2 spaces)
- Use single quotes for strings
- Always use semicolons at the end of statements
- Follow Vue 3 best practices for component structure
