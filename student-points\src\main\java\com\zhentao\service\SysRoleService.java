package com.zhentao.service;

import com.zhentao.pojo.SysRole;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.utils.Result;

import java.util.List;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface SysRoleService extends IService<SysRole> {
    Result findAllRole();

    SysRole findRoleById(Integer roleId);

    Result findAllPermission();

    Result findRolePermission(Integer roleId);

    Result addRole(SysRole sysRole);

    Result updateRole(SysRole sysRole);

    Result deleteRole(Integer roleId);

    Result updateRolePermission(Integer roleId, List<Integer> menuIds);
    
    /**
     * 查询角色关联的用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    Result findRoleUsers(Integer roleId);
}
