package com.zhentao.utils;

import com.zhentao.pojo.SysUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * <AUTHOR> @date 2025/5/22 9:03
 * @week星期四
 * @ClassName UserContext
 */

public class UserContext {
    private static final ThreadLocal<SysUser> userThreadLocal=new ThreadLocal<>();
    
    public static void setUserThreadLocal(SysUser user){
        userThreadLocal.set(user);
    }
    
    public static SysUser getCurrentUser(){
        // 首先尝试从ThreadLocal获取
        SysUser user = userThreadLocal.get();
        if (user != null) {
            return user;
        }
        
        // 如果ThreadLocal中没有，尝试从SecurityContext获取
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                Object principal = authentication.getPrincipal();
                if (principal instanceof SysUser) {
                    return (SysUser) principal;
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        
        return null;
    }
    
    /**
     * 获取当前用户ID
     * @return 用户ID，如果未登录则返回null
     */
    public static Integer getCurrentUserId() {
        SysUser user = getCurrentUser();
        return user != null ? user.getUserId() : null;
    }
    
    /**
     * 安全地获取用户ID，无需担心空指针异常
     * @param defaultValue 如果无法获取用户ID，返回的默认值
     * @return 用户ID或默认值
     */
    public static Integer getCurrentUserIdSafe(Integer defaultValue) {
        Integer userId = getCurrentUserId();
        return userId != null ? userId : defaultValue;
    }
    
    public static void clear(){
        userThreadLocal.remove();
    }
}
