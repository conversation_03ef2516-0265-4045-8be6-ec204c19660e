package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.mapper.IntegralRulesMapper;
import com.zhentao.pojo.IntegralRules;
import com.zhentao.service.IntegralRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class IntegralRulesServiceImpl extends ServiceImpl<IntegralRulesMapper, IntegralRules> implements IntegralRulesService {

    @Autowired
    private IntegralRulesMapper integralRulesMapper;
    @Override
    public Page<IntegralRules> findAllJia(@RequestBody IntegralRules integralRules) {
        Page<IntegralRules> page = new Page<>(integralRules.getPageNum(), integralRules.getPageSize());
        QueryWrapper<IntegralRules> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(integralRules.getMhc() != null, "rule_name", integralRules.getMhc());
        queryWrapper.like(integralRules.getMhc() != null, "description", integralRules.getMhc());
        queryWrapper.gt("score", 0);
        Page<IntegralRules> integralRulesPage = integralRulesMapper.selectPage(page, queryWrapper);
        return integralRulesPage;
    }

    @Override
    public Page<IntegralRules> findAllJian(IntegralRules integralRules) {
        Page<IntegralRules> page = new Page<>(integralRules.getPageNum(), integralRules.getPageSize());
        QueryWrapper<IntegralRules> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(integralRules.getMhc() != null, "rule_name", integralRules.getMhc());
        queryWrapper.like(integralRules.getMhc() != null, "description", integralRules.getMhc());
        queryWrapper.lt( "score", 0);
        Page<IntegralRules> integralRulesPage = integralRulesMapper.selectPage(page, queryWrapper);
        return integralRulesPage;
    }
}
