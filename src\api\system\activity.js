import request from '@/request/index'

// 查询活动
export function findActivity() {
  return request.post('/activity/findActivity')
}
// 添加活动
export function addActivity(data) {
  return request.post('/activity/addActivity', data)
}
// 修改活动
export function updateActivity(data) {
  return request.post('/activity/updateActivity', data)
}
// 删除活动
export function deleteActivity(activityId) {
  return request.post('/activity/deleteActivity', null, { params: { activityId } })
}
// 修改活动状态
export function changeActivityStatus(activityId, status) {
  return request.post('/activity/changeActivityStatus', null, { params: { activityId, status } })
}
// 上传活动图片
export function uploadActivityImage(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request.post('/activity/uploadFile', formData)
} 