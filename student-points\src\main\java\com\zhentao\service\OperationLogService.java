package com.zhentao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.pojo.OperationLog;
import com.zhentao.utils.Result;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface OperationLogService extends IService<OperationLog> {

    Result findLogPage(@RequestBody OperationLog operationLog);

    /**
     * 记录操作日志
     * @param username 用户名
     * @param realName 真实姓名
     * @param operationType 操作类型
     * @param module 操作模块
     * @param description 操作描述
     * @param status 操作状态（1-成功，2-失败）
     * @param request HTTP请求对象
     */
    void recordLog(String username, String realName, Integer operationType,
                   String module, String description, Integer status,
                   HttpServletRequest request);

    /**
     * 清空操作日志
     * @return 操作结果
     */
    Result clearAllLogs();

    /**
     * 导出操作日志
     * @param operationLog 查询条件
     * @return 日志数据列表
     */
    Result exportLogs(OperationLog operationLog);

    /**
     * 批量删除操作日志
     * @param ids 要删除的日志ID列表
     * @return 操作结果
     */
    Result batchDeleteLogs(List<Integer> ids);
}
