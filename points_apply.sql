/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:49:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for points_apply
-- ----------------------------
DROP TABLE IF EXISTS `points_apply`;
CREATE TABLE `points_apply`  (
  `apply_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `student_id` int(20) NOT NULL COMMENT '学生ID',
  `class_id` int(11) NULL DEFAULT NULL COMMENT '班级ID',
  `apply_user_id` int(20) NOT NULL COMMENT '申请人ID',
  `points_change` int(11) NOT NULL COMMENT '变动状态（正数为加分，负数为减分）',
  `points` int(11) NULL DEFAULT NULL COMMENT '分值',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请理由',
  `evidence_images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证明图片URL，多个以逗号分隔',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-待审核，1-已通过，2-已拒绝）',
  `reviewer_id` int(20) NULL DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `review_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`apply_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分变动申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of points_apply
-- ----------------------------
INSERT INTO `points_apply` VALUES (1, 1, 14, 1, 0, 5, '11', NULL, 0, 1, NULL, NULL, 1, '2025-07-08 16:31:18', NULL, '2025-07-08 17:10:15', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:49:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for edu_class
-- ----------------------------
DROP TABLE IF EXISTS `edu_class`;
CREATE TABLE `edu_class`  (
  `class_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '班级名称',
  `stage_id` int(20) NOT NULL COMMENT '所属阶段ID',
  `classroom` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '教室号',
  `teacher_id` int(20) NULL DEFAULT NULL COMMENT '讲师ID',
  `counselor_id` int(20) NULL DEFAULT NULL COMMENT '导员ID',
  `director_id` int(20) NULL DEFAULT NULL COMMENT '阶段主任ID',
  `start_date` date NULL DEFAULT NULL COMMENT '开班日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-结束）',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`class_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '班级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of edu_class
-- ----------------------------
INSERT INTO `edu_class` VALUES (1, '2505A', 13, '608东', 14, 20, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:02', NULL, '2025-07-07 15:22:23', NULL, 0);
INSERT INTO `edu_class` VALUES (2, '2504A', 14, '606', 13, 20, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:09', NULL, '2025-07-07 15:22:24', NULL, 0);
INSERT INTO `edu_class` VALUES (3, '2503A', 15, '603西', 12, 19, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:13', NULL, '2025-07-07 15:22:26', NULL, 0);
INSERT INTO `edu_class` VALUES (4, '2502A', 16, '608西', 11, 19, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:18', NULL, '2025-07-07 15:22:28', NULL, 0);
INSERT INTO `edu_class` VALUES (5, '2501A', 17, '602西', 10, 18, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:26', NULL, '2025-07-07 15:22:40', NULL, 0);
INSERT INTO `edu_class` VALUES (6, '2412A', 6, '610', 9, 18, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:42', NULL, '2025-07-07 15:22:41', NULL, 0);
INSERT INTO `edu_class` VALUES (7, '2411A', 7, '602东', 8, 16, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:47', NULL, '2025-07-07 15:22:48', NULL, 0);
INSERT INTO `edu_class` VALUES (8, '2410A', 8, '702', 7, 16, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:52', NULL, '2025-07-07 15:22:49', NULL, 0);
INSERT INTO `edu_class` VALUES (9, '2409A', 9, '611东', 6, 17, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:04:57', NULL, '2025-07-07 15:23:15', NULL, 0);
INSERT INTO `edu_class` VALUES (10, '2408A', 10, '611西', 5, 16, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:05:04', NULL, '2025-07-07 15:23:21', NULL, 0);
INSERT INTO `edu_class` VALUES (11, '2407A', 11, '605西', 4, 15, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:05:28', NULL, '2025-07-07 15:23:27', NULL, 0);
INSERT INTO `edu_class` VALUES (12, '2406A', 2, '718', 3, 15, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:05:34', NULL, '2025-07-07 15:23:28', NULL, 0);
INSERT INTO `edu_class` VALUES (13, '2405A', 3, '601', 2, 15, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:06:10', NULL, '2025-07-07 15:23:29', NULL, 0);
INSERT INTO `edu_class` VALUES (14, '2404A', 4, '612', 1, 15, NULL, NULL, NULL, 0, NULL, '2025-07-07 15:06:26', NULL, '2025-07-07 15:23:41', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:49:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for points_record
-- ----------------------------
DROP TABLE IF EXISTS `points_record`;
CREATE TABLE `points_record`  (
  `record_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `record_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '记录编号',
  `student_id` int(20) NOT NULL COMMENT '学生ID',
  `points_before` int(11) NOT NULL COMMENT '变动前积分',
  `points_change` int(11) NOT NULL COMMENT '积分变动值（正数为加分，负数为减分）',
  `points_after` int(11) NOT NULL COMMENT '变动后积分',
  `apply_id` int(20) NULL DEFAULT NULL COMMENT '关联的申请ID',
  `operator_id` int(20) NOT NULL COMMENT '操作人ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型（1-申请加分，2-申请减分，3-审核通过，4-审核拒绝，5-系统调整）',
  `operation_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变动原因',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '积分变动记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of points_record
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:49:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for edu_stage
-- ----------------------------
DROP TABLE IF EXISTS `edu_stage`;
CREATE TABLE `edu_stage`  (
  `stage_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '阶段ID',
  `stage_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '阶段名称',
  `course_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '课程名称',
  `parent_id` int(20) NULL DEFAULT 0 COMMENT '父阶段ID，0表示一级阶段',
  `stage_order` int(11) NOT NULL COMMENT '阶段顺序',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-禁用）',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`stage_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '阶段表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of edu_stage
-- ----------------------------
INSERT INTO `edu_stage` VALUES (1, '实训阶段', NULL, 0, 1, 0, NULL, '2025-07-07 14:58:23', NULL, '2025-07-07 14:58:23', NULL, 0);
INSERT INTO `edu_stage` VALUES (2, '实训一', '企业级云计算项目实训一', 1, 1, 0, NULL, '2025-07-07 14:58:34', NULL, '2025-07-07 16:12:32', NULL, 0);
INSERT INTO `edu_stage` VALUES (3, '实训二', '企业级云计算项目实训二', 1, 2, 0, NULL, '2025-07-07 14:58:34', NULL, '2025-07-07 16:12:29', NULL, 0);
INSERT INTO `edu_stage` VALUES (4, '实训三', '企业级云计算项目实训三', 1, 3, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 16:12:26', NULL, 0);
INSERT INTO `edu_stage` VALUES (5, '专高阶段', NULL, 0, 2, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 14:58:35', NULL, 0);
INSERT INTO `edu_stage` VALUES (6, '专高一', 'SSM+Vue3 基础', 5, 1, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 16:12:51', NULL, 0);
INSERT INTO `edu_stage` VALUES (7, '专高二', 'Spring boot+Vue3 高级', 5, 2, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 16:12:47', NULL, 0);
INSERT INTO `edu_stage` VALUES (8, '专高三', 'Spring Cloud 微服务开发', 5, 3, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 16:12:45', NULL, 0);
INSERT INTO `edu_stage` VALUES (9, '专高四', '微服务项目-新闻资讯（上）', 5, 4, 0, NULL, '2025-07-07 14:58:35', NULL, '2025-07-07 16:12:40', NULL, 0);
INSERT INTO `edu_stage` VALUES (10, '专高五', '微服务项目-新闻资讯（下）', 5, 5, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 16:12:37', NULL, 0);
INSERT INTO `edu_stage` VALUES (11, '专高六', '企业项目实战', 5, 6, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 16:12:34', NULL, 0);
INSERT INTO `edu_stage` VALUES (12, '专业阶段', NULL, 0, 3, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 14:58:36', NULL, 0);
INSERT INTO `edu_stage` VALUES (13, '专业一', '前端基础', 12, 1, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 16:13:07', NULL, 0);
INSERT INTO `edu_stage` VALUES (14, '专业二', '前端高级', 12, 2, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 16:13:03', NULL, 0);
INSERT INTO `edu_stage` VALUES (15, '专业三', 'Java 基础', 12, 3, 0, NULL, '2025-07-07 14:58:36', NULL, '2025-07-07 16:12:59', NULL, 0);
INSERT INTO `edu_stage` VALUES (16, '专业四', 'Java 高级', 12, 4, 0, NULL, '2025-07-07 14:58:37', NULL, '2025-07-07 16:12:57', NULL, 0);
INSERT INTO `edu_stage` VALUES (17, '专业五', 'Java Web 开发 +Vue2', 12, 5, 0, NULL, '2025-07-07 14:58:37', NULL, '2025-07-07 16:12:54', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:50:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` int(20) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'M' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-禁用）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '首页', 0, 1, '/dashboard', 'M', NULL, 'dashboard', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (2, '学生管理', 0, 2, '/student', 'M', NULL, 'peoples', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (3, '积分管理', 0, 3, '/points', 'M', NULL, 'score', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (4, '申请审批', 0, 4, '/approval', 'M', NULL, 'check', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (5, '统计分析', 0, 5, '/analysis', 'M', NULL, 'chart', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (6, '系统设置', 0, 6, '/system', 'M', NULL, 'setting', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (7, '学生列表', 2, 1, '/student/list', 'C', 'student:list', 'user', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (8, '班级管理', 2, 2, '/student/class', 'C', 'student:class', 'tree', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (9, '批量导入', 2, 3, '/student/import', 'C', 'student:import', 'upload', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (10, '积分增加', 3, 1, '/points/add', 'C', 'points:add', 'plus', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (11, '积分扣除', 3, 2, '/points/deduct', 'C', 'points:deduct', 'minus', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (12, '积分历史', 3, 3, '/points/history', 'C', 'points:history', 'history', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (13, '积分规则', 3, 4, '/points/rules', 'C', 'points:rules', 'document', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (14, '待审批', 4, 1, '/approval/pending', 'C', 'approval:pending', 'hourglass', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (15, '已处理', 4, 2, '/approval/processed', 'C', 'approval:processed', 'finished', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (16, '我的申请', 4, 3, '/approval/my', 'C', 'approval:my', 'list', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (17, '学生积分分析', 5, 1, '/analysis/student', 'C', 'analysis:student', 'user-analysis', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (18, '班级积分分析', 5, 2, '/analysis/class', 'C', 'analysis:class', 'team-analysis', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (19, '积分趋势分析', 5, 3, '/analysis/trend', 'C', 'analysis:trend', 'trend-chart', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (20, '数据导出', 5, 4, '/analysis/export', 'C', 'analysis:export', 'download', 0, '2025-07-08 14:43:08', '2025-07-08 14:43:08', NULL, 0);
INSERT INTO `sys_menu` VALUES (21, '用户管理', 6, 1, '/system/user', 'C', 'system:user', 'user-manage', 0, '2025-07-08 14:43:09', '2025-07-08 14:43:09', NULL, 0);
INSERT INTO `sys_menu` VALUES (22, '角色权限', 6, 2, '/system/role', 'C', 'system:role', 'role-manage', 0, '2025-07-08 14:43:09', '2025-07-08 14:43:09', NULL, 0);
INSERT INTO `sys_menu` VALUES (23, '系统参数', 6, 3, '/system/config', 'C', 'system:config', 'config', 0, '2025-07-08 14:43:09', '2025-07-08 14:43:09', NULL, 0);
INSERT INTO `sys_menu` VALUES (24, '操作日志', 6, 4, '/system/log', 'C', 'system:log', 'log', 0, '2025-07-08 14:43:09', '2025-07-08 14:43:09', NULL, 0);
INSERT INTO `sys_menu` VALUES (25, '添加学生', 7, 1, '', 'F', 'student:add', '#', 0, '2025-07-08 14:47:02', '2025-07-08 15:02:04', NULL, 0);
INSERT INTO `sys_menu` VALUES (26, '导出学生excel', 7, 2, '', 'F', 'student:export', '#', 0, '2025-07-08 14:48:07', '2025-07-08 15:02:06', NULL, 0);
INSERT INTO `sys_menu` VALUES (27, '学生详情', 7, 3, '', 'F', 'student:detail', '#', 0, '2025-07-08 14:50:27', '2025-07-08 16:05:30', NULL, 0);
INSERT INTO `sys_menu` VALUES (28, '编辑学生', 7, 4, '', 'F', 'studnet:edit', '#', 0, '2025-07-08 14:51:32', '2025-07-08 15:02:12', NULL, 0);
INSERT INTO `sys_menu` VALUES (29, '删除学生', 7, 5, '', 'F', 'student:remove', '#', 0, '2025-07-08 14:56:32', '2025-07-08 15:02:38', NULL, 0);
INSERT INTO `sys_menu` VALUES (30, '批量删除学生', 7, 6, '', 'F', 'student:removes', '#', 0, '2025-07-08 14:58:11', '2025-07-08 15:02:51', NULL, 0);
INSERT INTO `sys_menu` VALUES (31, '班级添加', 8, 1, '', 'F', NULL, '#', 0, '2025-07-08 15:02:02', '2025-07-08 15:02:24', NULL, 0);
INSERT INTO `sys_menu` VALUES (32, '审批列表(未处理)', 4, 1, '/points-apply/list0', 'C', 'points-apply:list0', '#', 0, '2025-07-08 15:58:54', '2025-07-08 16:06:06', NULL, 0);
INSERT INTO `sys_menu` VALUES (33, '积分添加', 3, 1, '/edu-student/addPoints', 'C', 'edu-student:addPoints', 'plus', 0, '2025-07-08 16:01:18', '2025-07-08 16:03:48', NULL, 0);
INSERT INTO `sys_menu` VALUES (34, '审批列表(已处理)', 4, 2, '/points-apply/list1', 'C', 'points-apply:list1', '#', 0, '2025-07-08 16:04:12', '2025-07-08 16:06:09', NULL, 0);
INSERT INTO `sys_menu` VALUES (35, '查询积分前十学生', 17, 1, '/pointsApply/findLimit', 'F', 'points-apply:findLimit', '#', 0, '2025-07-08 16:18:12', '2025-07-08 17:04:32', NULL, 0);
INSERT INTO `sys_menu` VALUES (36, '用户分页查询', 21, 1, '/sysUser/getUserPage', 'C', 'sysUser:getUserPage', '#', 0, '2025-07-08 16:34:24', '2025-07-08 16:34:24', NULL, 0);
INSERT INTO `sys_menu` VALUES (37, '积分扣减', 0, 0, '/edu-student/minusPoints', 'M', 'edu-student:minusPoints', '#', 0, '2025-07-08 17:04:02', '2025-07-08 17:04:02', NULL, 0);
INSERT INTO `sys_menu` VALUES (38, '用户添加', 21, 2, '/sysUser/addUserRole', 'F', 'sysUser:addUserRole', '#', 0, '2025-07-08 17:09:36', '2025-07-08 17:09:36', NULL, 0);
INSERT INTO `sys_menu` VALUES (39, '修改用户与角色', 21, 3, '/sysUser/updateUserRole', 'F', 'sysUser:updateUserRole', '#', 0, '2025-07-08 18:39:20', '2025-07-08 18:40:57', NULL, 0);
INSERT INTO `sys_menu` VALUES (40, '删除用户', 21, 4, '/sysUser/deleteUserRole', 'F', 'sysUser:deleteUserRole', '#', 0, '2025-07-08 18:45:10', '2025-07-08 18:45:10', NULL, 0);
INSERT INTO `sys_menu` VALUES (41, '修改用户状态', 21, 5, '/sysUser/updateUserStatus', 'F', 'sysUser:updateUserStatus', '#', 0, '2025-07-08 18:46:42', '2025-07-08 18:46:57', NULL, 0);
INSERT INTO `sys_menu` VALUES (42, '重置用户密码', 21, 6, '/sysUser/resetUserPassword', 'F', 'sysUser:resetUserPassword', '#', 0, '2025-07-08 18:47:17', '2025-07-08 18:48:08', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:49:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for edu_student
-- ----------------------------
DROP TABLE IF EXISTS `edu_student`;
CREATE TABLE `edu_student`  (
  `student_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '学生ID',
  `student_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学号',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学生姓名',
  `gender` tinyint(1) NULL DEFAULT 0 COMMENT '性别（0-女，1-男）',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `class_id` int(20) NOT NULL COMMENT '班级ID',
  `points` int(11) NULL DEFAULT 100 COMMENT '综合积分，默认100分',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-休学，2-退学）',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`student_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of edu_student
-- ----------------------------
INSERT INTO `edu_student` VALUES (1, '20210501012', '刘涵泽', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (2, '20220702060', '裴琪', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (3, '20211102012', '李世阳', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (4, '20220404225', '唐子韩', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (5, '20220802048', '李雨晨', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (6, '20220802055', '赵波强', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (7, '20220802056', '郝利伟', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (8, '20230703043', '臧超超', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (9, '20210702076', '尚天赐', 0, NULL, NULL, 14, 100, 0, NULL, '2025-07-07 15:27:57', NULL, '2025-07-07 15:27:57', NULL, 0);
INSERT INTO `edu_student` VALUES (10, '20220702015', '乌丽娜', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (11, '20220302017', '郭紫梦', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (12, '20211202041', '李家逸', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (13, '20220302056', '云禹芮', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (14, '20220404214', '白润浩', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (15, '20220504030', '杨柯', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (16, '20220702105', '王卓煜', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:22', NULL, '2025-07-07 15:29:22', NULL, 0);
INSERT INTO `edu_student` VALUES (17, '20200601043', '张庆阳', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (18, '20200701041', '马景昌', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (19, '20210802074', '陈浩然', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (20, '20200801319', '薛祥旺', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (21, '20230503032', '穆思辰', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (22, '20221203009', '田晨阳', 0, NULL, NULL, 13, 100, 0, NULL, '2025-07-07 15:29:23', NULL, '2025-07-07 15:29:23', NULL, 0);
INSERT INTO `edu_student` VALUES (23, '20210701143', '李玉莹', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (24, '20210702043', '杨宏彬', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (25, '20231003012', '司尚玉', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (26, '20220404280', '王娅霖', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (27, '20231103014', '孙浩', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (28, '20200701098', '孙一民', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:48', NULL, '2025-07-07 15:29:48', NULL, 0);
INSERT INTO `edu_student` VALUES (29, '20210802061', '张璟璇', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (30, '20220504025', '高雨航', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (31, '20220702069', '张磊', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (32, '20220802028', '田峻铭', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (33, '20210201011', '宋佳佳', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (34, '20200801058', '郝壮伟', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (35, '20230803020', '仝仁杰', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (36, '20210601004', '李傲迟', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (37, '20210802011', '张少飞', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (38, '20210802050', '徐俊帅', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (39, '20210802086', '詹兴旺', 0, NULL, NULL, 12, 100, 0, NULL, '2025-07-07 15:29:49', NULL, '2025-07-07 15:29:49', NULL, 0);
INSERT INTO `edu_student` VALUES (40, '20210701286', '徐康乐', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (41, '20220702094', '于在钊', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (42, '20231003008', '付少君', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (43, '20201201007', '郑嘉博', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (44, '20220902015', '韩学伟', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (45, '20210501055', '李子阳', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (46, '20200701042', '李少康', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (47, '20220902021', '臧阳铄', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:06', NULL, '2025-07-07 15:31:06', NULL, 0);
INSERT INTO `edu_student` VALUES (48, '20210601087', '王永春', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (49, '20210501059', '白云飞', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (50, '20210601127', '冯佳佳', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (51, '20210701024', '曹林萱', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (52, '20220702071', '贺程航', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (53, '20220702059', '裴秀宇', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (54, '20230803008', '粟加杰', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (55, '20200801347', '张宇', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (56, '20220404200', '张真', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (57, '20220702113', '韩佳源', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:07', NULL, '2025-07-07 15:31:07', NULL, 0);
INSERT INTO `edu_student` VALUES (58, '20220802087', '张扬', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (59, '20220802029', '沈佳伟', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (60, '20220504092', '高晓凯', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (61, '20210601113', '杨子哲', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (62, '20200901003', '张倍嘉', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (63, '20200801322', '李琰', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (64, '20200601003', '申其卓', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (65, '20200701028', '凌加建', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (66, '20200701142', '唐腾达', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:08', NULL, '2025-07-07 15:31:08', NULL, 0);
INSERT INTO `edu_student` VALUES (67, '20210802035', '王韬博', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (68, '20200701044', '李世朝', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (69, '20200801261', '康晗钰', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (70, '20200801226', '赵贯廷', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (71, '20200701015', '冬一博', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (72, '20211002005', '王张鑫', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (73, '20220302041', '张宇轩', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (74, '20210802024', '王瑞杰', 0, NULL, NULL, 11, 100, 0, NULL, '2025-07-07 15:31:09', NULL, '2025-07-07 15:31:09', NULL, 0);
INSERT INTO `edu_student` VALUES (75, '20200801073', '杜林青', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (76, '20221102024', '冯垚', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (77, '20221102022', '李子涵', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (78, '20210601111', '任冰倩', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (79, '20210701249', '何福帅', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (80, '20210701026', '魏金博', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (81, '20220802106', '杨项仂', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (82, '20220802078', '苏钰翔', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (83, '20231103031', '刘家源', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:18', NULL, '2025-07-07 15:32:18', NULL, 0);
INSERT INTO `edu_student` VALUES (84, '20220902056', '栾胜鑫', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (85, '20210701151', '张世涵', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (86, '20220802099', '杨智博', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (87, '20210701125', '孙杉杉', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (88, '20220902025', '郑晨耀', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (89, '20210601131', '常帅磊', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (90, '20210701149', '李昕原', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (91, '20200701043', '李世龙', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (92, '20200601031', '刘晓颖', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (93, '20220702086', '徐振闯', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:19', NULL, '2025-07-07 15:32:19', NULL, 0);
INSERT INTO `edu_student` VALUES (94, '20210802034', '马中骏', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (95, '20201201017', '纵静芸', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (96, '20200801371', '冯易寒', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (97, '20210601070', '郭丽娜', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (98, '20220702093', '金聪', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (99, '20210501083', '高辽宁', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (100, '20200801324', '杨旭朋', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (101, '20220404222', '冯婷婷', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (102, '20211002027', '许子浩', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (103, '20220202008', '王梓阳', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:20', NULL, '2025-07-07 15:32:20', NULL, 0);
INSERT INTO `edu_student` VALUES (104, '20200901040', '孙超林', 0, NULL, NULL, 10, 100, 0, NULL, '2025-07-07 15:32:21', NULL, '2025-07-07 15:32:21', NULL, 0);
INSERT INTO `edu_student` VALUES (105, '20230202034', '魏子纹', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (106, '20210701261', '杨佳琪', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-08 15:14:51', NULL, 0);
INSERT INTO `edu_student` VALUES (107, '20231003019', '周明震', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (108, '20210901017', '刘晋羽', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (109, '20221002004', '周子腾', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (110, '20220302013', '崔相宜', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (111, '20210701087', '辛佳音', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (112, '20221002028', '郭宸玮', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:12', NULL, '2025-07-07 15:33:12', NULL, 0);
INSERT INTO `edu_student` VALUES (113, '20230803058', '宋季桐', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (114, '20210701250', '孙硕', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (115, '20210601098', '王佳业', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (116, '20210701012', '李欣然', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (117, '20220802108', '焦子恒', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (118, '20210601129', '何依硕', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (119, '20210701369', '刘俊哲', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (120, '20210701283', '梁振宇', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:13', NULL, '2025-07-07 15:33:13', NULL, 0);
INSERT INTO `edu_student` VALUES (121, '20220902024', '张清硕', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (122, '20210701303', '赵忠杰', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (123, '20210801022', '李佳隆', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', 6, '2025-07-08 17:08:51', NULL, 0);
INSERT INTO `edu_student` VALUES (124, '20210801098', '王佳萱', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (125, '20210201086', '刘帅', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (126, '20210501087', '赵少阳', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (127, '20220702073', '张鑫楠', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (128, '20221102013', '吴瑞琛', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (129, '20210901027', '仝雅菲', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (130, '20210901030', '王恒飞', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (131, '20210501139', '韩帅龙', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (132, '20200901014', '何坤鹏', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:14', NULL, '2025-07-07 15:33:14', NULL, 0);
INSERT INTO `edu_student` VALUES (133, '20200701109', '邹琦', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (134, '20220902030', '陈明毅', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (135, '20210801095', '田依曼', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (136, '20220802115', '蔡洋泽', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (137, '20210701152', '王雨灿', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (138, '20210401024', '陈宇航', 0, NULL, NULL, 9, 100, 0, NULL, '2025-07-07 15:33:15', NULL, '2025-07-07 15:33:15', NULL, 0);
INSERT INTO `edu_student` VALUES (139, '20210601058', '袁子豪', 0, NULL, NULL, 9, 100, 1, NULL, '2025-07-07 15:33:15', NULL, '2025-07-08 15:15:11', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:50:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(11) NOT NULL COMMENT '显示顺序',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-禁用）',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, 0, 1, '2023-01-01 10:00:00', 1, '2023-01-01 10:00:00', '拥有所有权限', 0);
INSERT INTO `sys_role` VALUES (2, '普通管理员', 'manager', 2, 0, 1, '2023-01-02 11:00:00', 1, '2023-01-02 11:00:00', '拥有部分管理权限', 0);
INSERT INTO `sys_role` VALUES (3, '用户', 'user', 3, 0, 1, '2023-01-03 12:00:00', 1, '2023-01-03 12:00:00', '普通用户权限', 0);
INSERT INTO `sys_role` VALUES (4, '测试角色', 'test', 4, 1, 2, '2023-01-04 13:00:00', 2, '2023-01-04 13:00:00', '测试用角色，已禁用', 0);
INSERT INTO `sys_role` VALUES (5, '已删除角色', 'deleted', 5, 0, 2, '2023-01-05 14:00:00', 2, '2023-01-05 14:00:00', '示例删除记录', 1);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:50:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` int(20) NOT NULL COMMENT '角色ID',
  `menu_id` int(20) NOT NULL COMMENT '菜单ID'
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 1);
INSERT INTO `sys_role_menu` VALUES (1, 2);
INSERT INTO `sys_role_menu` VALUES (1, 3);
INSERT INTO `sys_role_menu` VALUES (1, 4);
INSERT INTO `sys_role_menu` VALUES (1, 5);
INSERT INTO `sys_role_menu` VALUES (1, 6);
INSERT INTO `sys_role_menu` VALUES (1, 7);
INSERT INTO `sys_role_menu` VALUES (1, 8);
INSERT INTO `sys_role_menu` VALUES (1, 9);
INSERT INTO `sys_role_menu` VALUES (1, 10);
INSERT INTO `sys_role_menu` VALUES (1, 11);
INSERT INTO `sys_role_menu` VALUES (1, 12);
INSERT INTO `sys_role_menu` VALUES (1, 13);
INSERT INTO `sys_role_menu` VALUES (1, 14);
INSERT INTO `sys_role_menu` VALUES (1, 15);
INSERT INTO `sys_role_menu` VALUES (1, 16);
INSERT INTO `sys_role_menu` VALUES (1, 17);
INSERT INTO `sys_role_menu` VALUES (1, 18);
INSERT INTO `sys_role_menu` VALUES (1, 19);
INSERT INTO `sys_role_menu` VALUES (1, 20);
INSERT INTO `sys_role_menu` VALUES (1, 21);
INSERT INTO `sys_role_menu` VALUES (1, 22);
INSERT INTO `sys_role_menu` VALUES (1, 23);
INSERT INTO `sys_role_menu` VALUES (1, 24);
INSERT INTO `sys_role_menu` VALUES (1, 32);
INSERT INTO `sys_role_menu` VALUES (1, 33);
INSERT INTO `sys_role_menu` VALUES (1, 34);
INSERT INTO `sys_role_menu` VALUES (1, 35);
INSERT INTO `sys_role_menu` VALUES (1, 36);
INSERT INTO `sys_role_menu` VALUES (1, 37);
INSERT INTO `sys_role_menu` VALUES (1, 37);
INSERT INTO `sys_role_menu` VALUES (1, 38);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:50:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '真实姓名',
  `gender` tinyint(1) NULL DEFAULT 0 COMMENT '性别（0-女，1-男）',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像路径',
  `user_type` tinyint(1) NOT NULL COMMENT '用户类型（1-讲师，2-导员，3-阶段主任，4-院长，9-超级管理员）',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（0-正常，1-禁用）',
  `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` int(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(20) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0-存在，1-删除）',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, '仵志松', '$2a$10$FfGWyiBgTtfV6Hbx3a0yWehWhqbQnEEv09/BFmaYC4RJqUzywhEPK', '仵志松', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:13:15', NULL, '2025-07-08 15:35:42', NULL, 0);
INSERT INTO `sys_user` VALUES (2, '徐得明', 'user', '徐得明', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:13:32', NULL, '2025-07-07 15:13:32', NULL, 0);
INSERT INTO `sys_user` VALUES (3, '韩国庆', 'user', '韩国庆', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:14:15', NULL, '2025-07-07 15:14:15', NULL, 0);
INSERT INTO `sys_user` VALUES (4, '黄清华', 'user', '黄清华', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:14:24', NULL, '2025-07-07 15:14:24', NULL, 0);
INSERT INTO `sys_user` VALUES (5, '孙世博', 'user', '孙世博', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:14:39', NULL, '2025-07-07 15:14:39', NULL, 0);
INSERT INTO `sys_user` VALUES (6, '张新龙', '$2a$10$FfGWyiBgTtfV6Hbx3a0yWehWhqbQnEEv09/BFmaYC4RJqUzywhEPK', '张新龙', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:14:49', NULL, '2025-07-08 15:14:49', NULL, 0);
INSERT INTO `sys_user` VALUES (7, '黄昊林', 'user', '黄昊林', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:15:00', NULL, '2025-07-07 15:15:00', NULL, 0);
INSERT INTO `sys_user` VALUES (8, '徐建辉', 'user', '徐建辉', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:15:20', NULL, '2025-07-07 15:15:20', NULL, 0);
INSERT INTO `sys_user` VALUES (9, '赵振乾', 'user', '赵振乾', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:15:34', NULL, '2025-07-07 15:15:34', NULL, 0);
INSERT INTO `sys_user` VALUES (10, '翟瑞烽', 'user', '翟瑞烽', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:15:46', NULL, '2025-07-07 15:15:46', NULL, 0);
INSERT INTO `sys_user` VALUES (11, '黄昊林', 'user', '黄昊林', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:15:57', NULL, '2025-07-07 15:15:57', NULL, 0);
INSERT INTO `sys_user` VALUES (12, '王佳男', 'user', '王佳男', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:16:07', NULL, '2025-07-07 15:16:07', NULL, 0);
INSERT INTO `sys_user` VALUES (13, '杨雅谦', 'user', '杨雅谦', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:16:18', NULL, '2025-07-07 15:16:18', NULL, 0);
INSERT INTO `sys_user` VALUES (14, '贾娇娇', 'user', '贾娇娇', 0, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, '2025-07-07 15:16:34', NULL, '2025-07-07 15:16:34', NULL, 0);
INSERT INTO `sys_user` VALUES (15, '董清清', 'user', '董清清', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:17:13', NULL, '2025-07-07 15:18:26', NULL, 0);
INSERT INTO `sys_user` VALUES (16, '葛硕', 'user', '葛硕', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:17:27', NULL, '2025-07-07 15:18:21', NULL, 0);
INSERT INTO `sys_user` VALUES (17, '崔聪聪', 'user', '崔聪聪', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:17:35', NULL, '2025-07-07 15:18:15', NULL, 0);
INSERT INTO `sys_user` VALUES (18, '李栓成', 'user', '李栓成', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:17:54', NULL, '2025-07-07 15:18:14', NULL, 0);
INSERT INTO `sys_user` VALUES (19, '刘子扬', 'user', '刘子扬', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:18:12', NULL, '2025-07-07 15:18:12', NULL, 0);
INSERT INTO `sys_user` VALUES (20, '郝适', 'user', '郝适', 0, NULL, NULL, NULL, 2, 0, NULL, NULL, NULL, '2025-07-07 15:18:43', NULL, '2025-07-07 15:18:43', NULL, 0);
INSERT INTO `sys_user` VALUES (22, '陈明毅', '$2a$10$2IYTZmPUk9ydo6ok9jjjL.pYFphhWly1Tb5gbpbXqstWDDr56qPte', '陈明毅', 0, '167867826', '<EMAIL>', NULL, 1, 0, NULL, NULL, NULL, NULL, NULL, '2025-07-08 18:49:38', NULL, 0);

SET FOREIGN_KEY_CHECKS = 1;


/*
 Navicat Premium Dump SQL

 Source Server         : aaa
 Source Server Type    : MySQL
 Source Server Version : 50625 (5.6.25)
 Source Host           : obmt6t2w67onmti8-mi.aliyun-cn-beijing-internet.oceanbase.cloud:3306
 Source Schema         : jifen

 Target Server Type    : MySQL
 Target Server Version : 50625 (5.6.25)
 File Encoding         : 65001

 Date: 08/07/2025 18:50:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` int(20) NOT NULL COMMENT '用户ID',
  `role_id` int(20) NOT NULL COMMENT '角色ID'
) ENGINE = oceanbase CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (6, 1);
INSERT INTO `sys_user_role` VALUES (22, 2);

SET FOREIGN_KEY_CHECKS = 1;
