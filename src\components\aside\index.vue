<template>
  <div class="aside-wrapper">
    <el-menu
      :default-active="activeMenu"
      class="el-menu-vertical"
      :collapse="isCollapse"
      @open="handleOpen"
      @close="handleClose"
      router
    >
      <div class="logo-container">
        <img src="../../../public/images/logo.png" alt="Logo" class="logo" v-if="!isCollapse" />
        <img src="../../../public/images/logo.png" alt="Logo" class="logo-small" v-else />
      </div>

      <el-menu-item index="/dashboard">
        <el-icon><HomeFilled /></el-icon>
        <template #title>首页</template>
      </el-menu-item>

      <el-sub-menu index="3">
        <template #title>
          <el-icon><Document /></el-icon>
          <span>申请审批</span>
        </template>
        <el-menu-item index="/dashboard/approval/pending">待审批</el-menu-item>
        <el-menu-item index="/dashboard/approval/processed">已处理</el-menu-item>
        <el-menu-item index="/dashboard/approval/my">我的申请</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="1">
        <template #title>
          <el-icon><User /></el-icon>
          <span>学生管理</span>
        </template>
        <el-menu-item index="/dashboard/student/list">学生列表</el-menu-item>
        <el-menu-item index="/dashboard/student/class">班级管理</el-menu-item>
        <el-menu-item index="/dashboard/student/imports">批量导入</el-menu-item>
        <el-menu-item index="/dashboard/student/import">导入学生</el-menu-item>
        <el-menu-item index="/dashboard/student/imp">归档信息</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="2">
        <template #title>
          <el-icon><Coin /></el-icon>
          <span>积分管理</span>
        </template>
        <el-menu-item index="/dashboard/points/add">积分增加</el-menu-item>
        <el-menu-item index="/dashboard/points/deduct">积分扣除</el-menu-item>
        <el-menu-item index="/dashboard/points/history">积分历史</el-menu-item>
        <el-menu-item index="/dashboard/points/rules">积分规则</el-menu-item>
        <el-menu-item index="/dashboard/points/activity">活动管理</el-menu-item>
        <el-menu-item index="/dashboard/points/application">报名管理</el-menu-item>
      </el-sub-menu>

      <el-sub-menu index="5">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </template>
        <el-menu-item index="/dashboard/settings/profile">个人信息</el-menu-item>
        <el-menu-item index="/dashboard/settings/password">修改密码</el-menu-item>
        <el-menu-item index="/dashboard/settings/user" v-show="roleIds.includes(1)"
          >用户管理</el-menu-item
        >
        <el-menu-item index="/dashboard/settings/role" v-show="roleIds.includes(1)"
          >角色权限</el-menu-item
        >
        <el-menu-item index="/dashboard/settings/about">关于我们</el-menu-item>
        <el-menu-item index="/dashboard/log/operation">操作日志</el-menu-item>
      </el-sub-menu>
    </el-menu>
  </div>
</template>

<script setup>
  import { ref, computed, inject } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    HomeFilled,
    User,
    Coin,
    Document,
    Setting,
    Van,
    MagicStick
  } from '@element-plus/icons-vue';

  const route = useRoute();
  const isCollapse = inject('isSidebarCollapsed', ref(false));
  const roleIds = localStorage.getItem('roleId');
  console.log(roleIds);
  const activeMenu = computed(() => {
    return route.path;
  });

  const handleOpen = (key, keyPath) => {
    // 菜单展开时的处理
    console.log('Menu opened:', key, keyPath);
  };

  const handleClose = (key, keyPath) => {
    // 菜单关闭时的处理
    console.log('Menu closed:', key, keyPath);
  };
</script>

<style scoped>
  .aside-wrapper {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* 隐藏WebKit浏览器的滚动条 */
  .aside-wrapper::-webkit-scrollbar {
    display: none;
  }

  .el-menu-vertical {
    height: auto;
    min-height: 100%;
    border-right: none;
  }

  .el-menu-vertical:not(.el-menu--collapse) {
    width: 240px;
  }

  .logo-container {
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
    position: sticky;
    top: 0;
    background-color: inherit;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .logo {
    height: 130px;
    margin: 0 auto;
  }

  .logo-small {
    height: 30px;
    margin: 0 auto;
  }

  /* 确保滚动平滑 */
  .aside-wrapper {
    scroll-behavior: smooth;
  }

  /* 优化菜单项的高度和间距 */
  :deep(.el-menu-item),
  :deep(.el-sub-menu__title) {
    height: 50px;
    line-height: 50px;
  }

  /* 美化菜单项 */
  :deep(.el-menu-item) {
    position: relative;
    transition: all 0.3s;
  }

  :deep(.el-menu-item.is-active) {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409eff;
    font-weight: bold;
  }

  :deep(.el-menu-item.is-active::before) {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background-color: #409eff;
  }

  :deep(.el-menu-item:hover),
  :deep(.el-sub-menu__title:hover) {
    background-color: rgba(64, 158, 255, 0.05);
  }

  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: #409eff;
  }

  :deep(.el-icon) {
    margin-right: 8px;
    font-size: 18px;
  }
</style>
