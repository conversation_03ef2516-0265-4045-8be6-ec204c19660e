import request from "@/request/index.js";
export function list2(data){
    return request({
        url:"/points-apply/list2",
        method:"post",
        data:data
    })
}
export function list1(data){
    return request({
        url:"/points-apply/list1",
        method:"post",
        data:data
    })
}
export function list0(data){
    return request({
        url:"/points-apply/list0",
        method:"post",
        data:data
    })
}
export function banji(){
    return request({
        url:"/points-apply/class",
        method:"post"
    })
}
export function tg(id){
    return request({
        url:"/points-apply/tg?id="+id,
        method:"post"
    })
}
export function jj(id){
    return request({
        url:"/points-apply/jj?id="+id,
        method:"post"
    })
}