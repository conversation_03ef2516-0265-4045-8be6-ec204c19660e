<template>
  <div class="header-container">
    <div class="header-left">
      <el-button type="text" @click="toggleSidebar">
        <el-icon size="20px"><Fold v-if="!isSidebarCollapsed" /><Expand v-else /></el-icon>
      </el-button>
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentRoute.meta.title">{{ currentRoute.meta.title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>
    
    <div class="header-right">
      <el-tooltip content="消息通知" placement="bottom">
        <el-badge :value="unreadCount" class="header-icon" v-if="unreadCount > 0">
          <el-icon size="20px" @click="openNotificationDialog"><Bell /></el-icon>
        </el-badge>
        <el-icon size="20px" class="header-icon" v-else @click="openNotificationDialog"><Bell /></el-icon>
      </el-tooltip>

      <!-- 暗色模式切换按钮 -->
      <el-tooltip :content="appStore.isDarkMode ? '切换为明亮模式' : '切换为暗色模式'" placement="bottom">
        <el-button class="header-icon" circle size="small" @click="toggleDarkMode">
          <el-icon>
            <template v-if="!appStore.isDarkMode">🌙</template>
            <template v-else>☀</template>
          </el-icon>
        </el-button>
      </el-tooltip>
      <el-dialog v-model="showNotificationDialog" title="积分变动通知" width="400px" @open="onDialogOpen">
        <div v-if="messages.length === 0" style="text-align:center;color:#888;">暂无新加减积分消息</div>
        <el-timeline v-else>
          <el-timeline-item v-for="msg in messages" :key="msg.id" :timestamp="msg.time" :color="msg.type === 'add' ? 'green' : 'red'">
            <span v-if="msg.type === 'add'">[加分]</span>
            <span v-else>[减分]</span>
            {{ msg.content }}
          </el-timeline-item>
        </el-timeline>
        <template #footer>
          <el-button type="primary" @click="showNotificationDialog = false">关闭</el-button>
        </template>
      </el-dialog>
      
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="user-profile">
          <el-avatar :size="32" :src="avatar" />
          <span class="user-name">{{ username }}</span>
          <el-icon><CaretBottom /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人信息</el-dropdown-item>
            <el-dropdown-item command="password">修改密码</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, inject, onMounted, onUnmounted, watch} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Fold, 
  Expand, 
  Bell, 
  CaretBottom
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { logout } from '@/api/system/user.js'
import { getCurrentUser, clearUserData } from '@/utils/userUtils.js'
import {useCounterStore} from "@/stores/counter.js";
import {storeToRefs} from "pinia";
import { useAppStore } from '@/stores/appStore.js'

const route = useRoute()
const router = useRouter()
const isSidebarCollapsed = inject('isSidebarCollapsed', ref(false))
const username =ref(localStorage.getItem("username"));
const avatar =ref(localStorage.getItem("avatar"));
// 监听头像更新事件
const updateAvatar = () => {
  avatar.value = localStorage.getItem("avatar")
}


onUnmounted(() => {
  window.removeEventListener('avatar-updated', updateAvatar)
})
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// User name from stored user data

// Get current user information on component mount
onMounted(() => {
 username.value
  avatar.value
  window.addEventListener('avatar-updated', updateAvatar)
})

const currentRoute = computed(() => {
  return route
})

const unreadCount = ref(3) // 模拟未读消息数
const showNotificationDialog = ref(false)
const messages = ref([
  { id: 1, content: '李欣然获得加分10分，因参加志愿服务', type: 'add', time: '2025-07-20 21:14' },
  { id: 2, content: '王佳业被扣分5分，因迟到', type: 'deduct', time: '2025-07-17 17:05' },
  { id: 3, content: '刘涵泽获得加分8分，因表现优异', type: 'add', time: '2025-07-17 17:04' }
])

function openNotificationDialog() {
  showNotificationDialog.value = true
}
function onDialogOpen() {
  if (unreadCount.value > 0) unreadCount.value = 0
}

const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    .then(async () => {
      try {
        // 调用登出API
        await logout();
        // 清除登录信息
        clearUserData();
        // 跳转到登录页
        router.push('/');
        ElMessage({
          type: 'success',
          message: '已成功退出登录',
        });
      } catch (error) {
        console.error('登出失败:', error);
        
        // 即使API调用失败，也强制清除本地登录状态
        clearUserData();
        router.push('/');
        
        ElMessage({
          type: 'warning',
          message: '登出过程中出现问题，已强制退出登录',
        });
      }
    })
    .catch(() => {});
  } else if (command === 'profile') {
    router.push('/dashboard/settings/profile');
  } else if (command === 'password') {
    router.push('/dashboard/settings/password');
  }
}

const appStore = useAppStore()

// 暗色模式切换逻辑
const toggleDarkMode = () => {
  appStore.toggleDarkMode()
}
watch(() => appStore.isDarkMode, (val) => {
  if (val) {
    document.body.classList.add('dark-mode')
  } else {
    document.body.classList.remove('dark-mode')
  }
}, { immediate: true })
</script>

<style scoped>
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
}

.breadcrumb {
  margin-left: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 20px;
  cursor: pointer;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-name {
  margin: 0 8px;
  font-size: 14px;
}
/* 暗色模式样式 */
:global(.dark-mode) .header-container {
  background-color: #23262f !important;
  color: #e0e0e0 !important;
  border-bottom: 1px solid #333 !important;
}
:global(.dark-mode) .el-breadcrumb__inner {
  color: #e0e0e0 !important;
}
:global(.dark-mode) .user-profile {
  color: #e0e0e0 !important;
}
:global(.dark-mode) .el-dialog {
  background: #23262f !important;
  color: #e0e0e0 !important;
}
:global(.dark-mode) .el-timeline-item__content {
  color: #e0e0e0 !important;
}
:global(.dark-mode) .el-dropdown-menu {
  background: #23262f !important;
  color: #e0e0e0 !important;
}
</style>