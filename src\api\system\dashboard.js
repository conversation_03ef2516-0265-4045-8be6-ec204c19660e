import request from '@/request';

/**
 * 获取首页统计数据
 * @returns {Promise}
 */
export function getDashboardStats() {
  return request({
    url: '/dashboard/stats',
    method: 'get'
  });
}

/**
 * 获取班级积分排名
 * @param {Number} limit 限制数量，默认为5
 * @returns {Promise}
 */
export function getClassRankings(limit = 5) {
  return request({
    url: '/dashboard/class-rankings',
    method: 'get',
    params: { limit }
  });
}

/**
 * 获取最近积分活动
 * @param {Number} limit 限制数量，默认为5
 * @returns {Promise}
 */
export function getRecentActivities(limit = 5) {
  return request({
    url: '/dashboard/recent-activities',
    method: 'get',
    params: { limit }
  });
}

/**
 * 获取积分申请统计
 * @returns {Promise}
 */
export function getPointsApplyStats() {
  return request({
    url: '/dashboard/points-apply-stats',
    method: 'get'
  });
}

/**
 * 获取学生积分概览
 * @returns {Promise}
 */
export function getStudentPointsOverview() {
  return request({
    url: '/dashboard/student-points-overview',
    method: 'get'
  });
} 