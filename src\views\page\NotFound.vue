<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="not-found-image">
        <div class="not-found-number">404</div>
        <div class="not-found-icon">
          <el-icon :size="80"><WarningFilled /></el-icon>
        </div>
      </div>
      <h2 class="not-found-title">页面不存在</h2>
      <p class="not-found-desc">抱歉，您访问的页面不存在或已被删除</p>
      <div class="not-found-actions">
        <el-button type="primary" size="large" @click="goHome">返回首页</el-button>
        <el-button size="large" @click="goBack">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { WarningFilled } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  background-color: #f5f7fa;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.6s ease-in-out;
}

.not-found-image {
  position: relative;
  height: 160px;
  margin-bottom: 20px;
}

.not-found-number {
  font-size: 160px;
  font-weight: bold;
  line-height: 1;
  color: #f0f2f5;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.not-found-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  color: #f56c6c;
  animation: pulse 2s infinite;
}

.not-found-title {
  font-size: 24px;
  color: #303133;
  margin: 0 0 10px;
}

.not-found-desc {
  font-size: 16px;
  color: #909399;
  margin: 0 0 30px;
}

.not-found-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .not-found-content {
    padding: 30px 20px;
  }
  
  .not-found-number {
    font-size: 120px;
  }
  
  .not-found-icon {
    transform: translate(-50%, -50%) scale(0.8);
  }
  
  .not-found-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .not-found-actions .el-button {
    width: 100%;
  }
}
</style> 