package com.zhentao.controller;

import com.zhentao.pojo.OperationLog;
import com.zhentao.service.OperationLogService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 操作日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@RequestMapping("/operation-log")
public class OperationLogController {
    @Autowired
    private OperationLogService operationLogService;
    /**
     * 分页查询操作日志
     * @param operationLog 查询条件
     * @return 分页结果
     */
    @PostMapping("/getOperationLogPage")
    public Result getOperationLogPage(@RequestBody OperationLog operationLog) {
        return operationLogService.findLogPage(operationLog);
    }

    /**
     * 清空所有操作日志
     * @return 操作结果
     */
    @PostMapping("/clearAllLogs")
    public Result clearAllLogs() {
        return operationLogService.clearAllLogs();
    }

    /**
     * 导出操作日志
     * @param operationLog 查询条件
     * @return 日志数据
     */
    @PostMapping("/exportLogs")
    public Result exportLogs(@RequestBody OperationLog operationLog) {
        return operationLogService.exportLogs(operationLog);
    }

    /**
     * 批量删除操作日志
     * @param ids 要删除的日志ID列表
     * @return 操作结果
     */
    @PostMapping("/batchDeleteLogs")
    public Result batchDeleteLogs(@RequestBody List<Integer> ids) {
        return operationLogService.batchDeleteLogs(ids);
    }

}
