<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import {
  getStudentList,
  addStudent,
  updateStudent,
  deleteStudent,
  getClassOptions,
  getStageOptions,
  saveArchive, clearUserAll
} from '@/api/system/student';
import axios from 'axios';
import { useRouter } from 'vue-router';

const router = useRouter();

// 班级选项
const classOptions = ref([])
// 专业选项
const majorOptions = ref([])

// 筛选表单
const filterForm = reactive({
  realName: '',
  studentNo: '',
  classId: null,
  stageName: '',
  gender: null
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref({})

// 添加/编辑学生对话框
const studentDialogVisible = ref(false)
const studentFormRef = ref(null)
const isEdit = ref(false)
const studentForm = reactive({
  studentId: null,
  studentNo: '',
  realName: '',
  gender: 1,
  classId: null,
  phone: '',
  email: '',
  points: 100,
  status: 0,
  remark: ''
})

const studentRules = {
  realName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  studentNo: [
    { required: true, message: '请输入学号', trigger: 'blur' },
    { pattern: /^\d{7,20}$/, message: '学号必须为7-20位数字', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  classId: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

onMounted(() => {
  // 加载班级选项
  fetchClassOptions()
  // 加载专业选项
  fetchStageOptions()
  // 加载学生数据
  fetchData()
})

// 方法定义
const fetchClassOptions = async () => {
  try {
    const token = localStorage.getItem("Authorization");
    if (!token) {
      ElMessage.error('您尚未登录或登录已过期，请重新登录');
      router.push('/login');
      return;
    }
    
    const res = await getClassOptions();
    if (res.data.code === 200) {
      classOptions.value = res.data.data.map(item => ({
        value: item.classId,
        label: item.className
      }));
    }
  } catch (error) {
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录');
      localStorage.removeItem('Authorization');
      router.push('/login');
    } else {
      ElMessage.error('获取班级信息失败');
    }
  }
}

// 获取专业选项
const fetchStageOptions = async () => {
  try {
    const token = localStorage.getItem("Authorization");
    if (!token) {
      ElMessage.error('您尚未登录或登录已过期，请重新登录');
      router.push('/login');
      return;
    }
    
    const res = await getStageOptions();
    if (res.data.code === 200) {
      // 确保专业名称干净且唯一
      const stageNames = new Set();
      majorOptions.value = res.data.data
        .filter(item => {
          const stageName = item.stageName.trim();
          if (!stageName || stageNames.has(stageName)) return false;
          stageNames.add(stageName);
          return true;
        })
        .map(item => ({
          value: item.stageName.trim(),
          label: item.stageName.trim(),
          id: item.stageId
        }));
      console.log('获取专业选项成功，共 ' + majorOptions.value.length + ' 个选项');
      
      // 按照专业名称排序
      majorOptions.value.sort((a, b) => a.label.localeCompare(b.label, 'zh-CN'));
    } else {
      console.error('获取专业选项失败:', res.data);
      fallbackStageOptions();
    }
  } catch (error) {
    console.error('获取专业信息失败:', error);
      fallbackStageOptions();
    }
}

// 备选方案：使用硬编码的专业列表
const fallbackStageOptions = () => {
      majorOptions.value = [
        { value: '实训一', label: '实训一' },
        { value: '实训二', label: '实训二' },
        { value: '实训三', label: '实训三' },
    { value: '专高一', label: '专高一' },
    { value: '专高二', label: '专高二' },
    { value: '专高三', label: '专高三' },
    { value: '专高四', label: '专高四' },
    { value: '专高五', label: '专高五' },
    { value: '专高六', label: '专高六' },
        { value: '专业一', label: '专业一' },
        { value: '专业二', label: '专业二' },
        { value: '专业三', label: '专业三' },
        { value: '专业四', label: '专业四' },
        { value: '专业五', label: '专业五' }
      ];
  console.log('使用备选专业选项');
}

const fetchData = async () => {
  loading.value = true;
  try {
    // 准备查询参数，确保专业名称干净
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      realName: filterForm.realName || null,
      studentNo: filterForm.studentNo || null,
      classId: filterForm.classId || null,
      stageName: filterForm.stageName ? filterForm.stageName.trim() : null,
      gender: filterForm.gender !== '' && filterForm.gender !== null ? filterForm.gender : null
    };
    
    console.log('发送查询参数:', params);
    const res = await getStudentList(params);
    
    if (res.data && res.data.records) {
      // 处理返回数据，确保专业名称干净
      tableData.value = res.data.records.map(record => ({
        ...record,
        stageName: record.stageName ? record.stageName.trim() : ''
      }));
      total.value = res.data.total;
      console.log(`查询成功，共 ${res.data.total} 条记录`);
      
      if (res.data.total === 0 && (filterForm.classId || filterForm.stageName)) {
        ElMessage.info('未找到符合条件的学生记录');
      }
    } else {
      tableData.value = [];
      total.value = 0;
      console.warn('查询结果为空');
      ElMessage.info('未找到符合条件的学生记录');
    }
  } catch (error) {
    console.error('查询失败:', error);
      ElMessage.error('获取学生列表失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
}

// 处理查询
const handleSearch = () => {
  console.log('执行查询，条件:', {
    姓名: filterForm.realName || '无',
    学号: filterForm.studentNo || '无',
    班级: filterForm.classId ? classOptions.value.find(c => c.value === filterForm.classId)?.label : '无',
    专业: filterForm.stageName || '无',
    性别: filterForm.gender === 1 ? '男' : filterForm.gender === 0 ? '女' : '无'
  });
  
  // 重置到第一页并刷新数据
  currentPage.value = 1;
  fetchData();
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.realName = '';
  filterForm.studentNo = '';
  filterForm.classId = null;
  filterForm.stageName = '';
  filterForm.gender = null;
  
  // 重置后自动查询
  handleSearch();
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

const refreshTable = () => {
  if(confirm("是否要将数据进行归档")){
    saveArchive().then(res=>{
      console.log(res);
      if(res.data.message=='添加成功'){
        ElMessage.success('操作成功')
        fetchData()
      }else{
        ElMessage.error('操作失败')
      }
    })
  }
}

const clearAll = () =>{
  clearUserAll().then(res=>{
    console.log(res);
    if(res.data.message=='清空成功'){
      ElMessage.success('操作成功')
      fetchData()
    }else{
      ElMessage.error('操作失败')
    }
  })
}

const getPointsClass = (points) => {
  if (points >= 90) return 'points-excellent'
  if (points >= 80) return 'points-good'
  if (points >= 60) return 'points-pass'
  return 'points-fail'
}

const viewDetail = (row) => {
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

const showAddDialog = () => {
  isEdit.value = false
  Object.keys(studentForm).forEach(key => {
    if (key === 'gender') {
      studentForm[key] = 1
    } else if (key === 'points') {
      studentForm[key] = 100
    } else if (key === 'status') {
      studentForm[key] = 0
    } else {
      studentForm[key] = null
    }
  })
  studentForm.studentId = null
  studentForm.studentNo = ''
  studentForm.realName = ''
  studentForm.phone = ''
  studentForm.email = ''
  studentForm.remark = ''
  
  studentDialogVisible.value = true
}

const editStudent = (row) => {
  isEdit.value = true
  studentForm.studentId = row.studentId
  studentForm.studentNo = row.studentNo
  studentForm.realName = row.realName
  studentForm.gender = row.gender
  studentForm.classId = row.classId
  studentForm.phone = row.phone
  studentForm.email = row.email
  studentForm.points = row.points
  studentForm.status = row.status
  studentForm.remark = row.remark
  
  studentDialogVisible.value = true
}

const submitStudentForm = async (formEl) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 编辑学生
          const res = await updateStudent(studentForm)
          if (res.data.message === '修改成功') {
            ElMessage.success('学生信息更新成功')
            studentDialogVisible.value = false
            await fetchData()
          } else {
            ElMessage.error(res.data.message || '修改失败')
          }
        } else {
          // 添加学生
          const res = await addStudent(studentForm)
          if (res.data.message === "添加成功") {
            ElMessage.success('学生添加成功')
            studentDialogVisible.value = false
            await fetchData()
          } else {
            ElMessage.error(res.data.message || '添加失败')
          }
        }
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
      }
    } else {
      return false
    }
  })
}

const deleteStudentHandler = (row) => {
  ElMessageBox.confirm(
    `确定要删除学生"${row.realName}"的信息吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(async () => {
    try {
      const res = await deleteStudent(row.studentId)
      if (res.data.message === "删除成功") {
        ElMessage.success('删除成功')
        await fetchData()
      } else {
        ElMessage.error(res.data.message || '删除失败')
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
  .catch(() => {})
}

const exportData = () => {
  ElMessageBox.confirm(
    '确定要导出当前筛选的学生信息吗？',
    '导出确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  )
  .then(async () => {
    try {
      loading.value = true;
      ElMessage.info('正在导出数据，请稍候...');
      
      // 准备查询参数
      const params = {
        realName: filterForm.realName || null,
        studentNo: filterForm.studentNo || null,
        classId: filterForm.classId || null,
        stageName: filterForm.stageName ? filterForm.stageName.trim() : null,
        gender: filterForm.gender !== '' && filterForm.gender !== null ? filterForm.gender : null
      };
      
      // 获取token
      const token = localStorage.getItem("Authorization");
      if (!token) {
        ElMessage.error('未登录或登录已过期，请重新登录');
        router.push('/login');
        return;
      }
      
      // 使用axios直接处理blob响应
      const response = await axios({
        url: '/api/edu-student/export',
        method: 'post',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        data: params,
        responseType: 'blob'
      });
      
      // 获取文件名
      const contentDisposition = response.headers['content-disposition'];
      let filename = '学生数据.xlsx';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)\.xlsx/);
        if (filenameMatch && filenameMatch.length > 1) {
          filename = decodeURIComponent(filenameMatch[1]) + '.xlsx';
        } else {
          // 获取当前日期作为文件名的一部分
          const date = new Date();
          const dateStr = `${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`;
          filename = `学生数据_${dateStr}.xlsx`;
        }
      }
      
      // 创建下载链接
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link); // 添加到body确保在Firefox中也能工作
      link.click();
      document.body.removeChild(link); // 清理
      URL.revokeObjectURL(link.href);
      
      ElMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      ElMessage.error('导出失败，请稍后再试');
    } finally {
      loading.value = false;
    }
  })
  .catch(() => {});
}
</script>

<template>
  <div class="student-list-container">
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>学生信息查询</h3>
          <el-button type="primary" @click="showAddDialog">添加学生</el-button>
        </div>
      </template>
      <el-form :model="filterForm" :inline="true" class="filter-form">
        <el-form-item label="姓名">
          <el-input v-model="filterForm.realName" placeholder="请输入姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="学号">
          <el-input v-model="filterForm.studentNo" placeholder="请输入学号" clearable></el-input>
        </el-form-item>
        <el-form-item label="班级">
          <el-select v-model="filterForm.classId" placeholder="请选择班级" clearable>
            <el-option v-for="item in classOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业">
          <el-select v-model="filterForm.stageName" placeholder="请选择专业" clearable>
            <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="filterForm.gender" placeholder="请选择性别" clearable>
            <el-option label="男" :value="1"></el-option>
            <el-option label="女" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>学生列表</h3>
          <div class="header-actions">
            <el-tooltip content="导出当前筛选条件下的学生数据" placement="top" effect="light">
              <el-button type="success" @click="exportData" :icon="Download" :loading="loading">
                {{ loading ? '导出中...' : '导出数据' }}
              </el-button>
            </el-tooltip>
            <el-button type="primary" @click="refreshTable" >点击归档</el-button>
            <el-button type="primary" @click="clearAll" >清空数据</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="studentNo" label="学号" width="120" sortable />
        <el-table-column prop="realName" label="姓名" width="100" />
        <el-table-column label="性别" width="60">
          <template #default="scope">
            {{ scope.row.gender === 1 ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="className" label="班级" width="150" />
        <el-table-column prop="stageName" label="专业" width="180" />
        <el-table-column prop="points" label="积分" width="80" sortable>
          <template #default="scope">
            <span :class="getPointsClass(scope.row.points)">{{ scope.row.points }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button link type="primary" size="small" @click="editStudent(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="deleteStudentHandler(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="学生详情" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">{{ currentDetail.realName }}</el-descriptions-item>
        <el-descriptions-item label="学号">{{ currentDetail.studentNo }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ currentDetail.gender === 1 ? '男' : '女' }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ currentDetail.className }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ currentDetail.stageName }}</el-descriptions-item>
        <el-descriptions-item label="积分">
          <span :class="getPointsClass(currentDetail.points)">{{ currentDetail.points }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentDetail.phone || '无' }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ currentDetail.email || '无' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentDetail.status === 0 ? 'success' : currentDetail.status === 1 ? 'warning' : 'danger'">
            {{ currentDetail.status === 0 ? '正常' : currentDetail.status === 1 ? '休学' : '退学' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentDetail.createTime || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentDetail.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="editStudent(currentDetail)">编辑</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 添加/编辑学生对话框 -->
    <el-dialog v-model="studentDialogVisible" :title="isEdit ? '编辑学生' : '添加学生'" width="60%">
      <el-form :model="studentForm" :rules="studentRules" ref="studentFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="realName">
              <el-input v-model="studentForm.realName" placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学号" prop="studentNo">
              <el-input v-model="studentForm.studentNo" placeholder="请输入学号" :disabled="isEdit"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="studentForm.gender">
                <el-radio :label="1">男</el-radio>
                <el-radio :label="0">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="班级" prop="classId">
              <el-select v-model="studentForm.classId" placeholder="请选择班级" style="width: 100%">
                <el-option v-for="item in classOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="studentForm.phone" placeholder="请输入联系电话"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="studentForm.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="积分" prop="points">
              <el-input-number v-model="studentForm.points" :min="0" :max="1000" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="studentForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option :value="0" label="正常"></el-option>
                <el-option :value="1" label="休学"></el-option>
                <el-option :value="2" label="退学"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="studentForm.remark" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="studentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStudentForm(studentFormRef)">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.student-list-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.student-list-container::-webkit-scrollbar {
  display: none;
}

.filter-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.filter-card:hover, .table-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-actions .el-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  min-width: 100px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.points-excellent {
  color: #67C23A;
  font-weight: bold;
}

.points-good {
  color: #409EFF;
  font-weight: bold;
}

.points-pass {
  color: #E6A23C;
}

.points-fail {
  color: #F56C6C;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>