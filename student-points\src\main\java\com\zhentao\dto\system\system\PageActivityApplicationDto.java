package com.zhentao.dto.system.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class PageActivityApplicationDto {
    private Integer pageCurrent;
    private Integer pageSize;
    private Integer activityId;
    private String applicationName;
    private String className;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date[] timeRange;
}
