package com.zhentao.service.impl;

import com.zhentao.pojo.SysMenu;
import com.zhentao.mapper.SysMenuMapper;
import com.zhentao.service.SysMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Override
    public Result findAllMenu() {
        //1.查询所有菜单数据
        List<SysMenu> allMenus = sysMenuMapper.selectList(null);
        if (allMenus == null || allMenus.isEmpty()){
            return Result.ERROR("未查询到菜单权限数据");
        }
        //2.构建树形结构菜单
        List<SysMenu> rootMenu=new ArrayList<>();
        for (SysMenu menu : allMenus) {
            if (menu.getParentId() == 0){//假设 parent_id为0表示一级菜单
                rootMenu.add(buildChildMenu(menu,allMenus));
            }
        }
        //3.封装结果返回
        return Result.OK(rootMenu);
    }
    /**
     * 递归构建子菜单
     * @param parentMenu 父级菜单
     * @parm addMenu 所有菜单列表
     * @return 包含子菜单的父级菜单
     */
    private SysMenu buildChildMenu(SysMenu parentMenu,List<SysMenu> allMenus){
        List<SysMenu> chlidMenus=new ArrayList<>();
        for (SysMenu menu : allMenus) {
            if (menu.getParentId().equals(parentMenu.getMenuId())){
                chlidMenus.add(buildChildMenu(menu,allMenus));
            }
        }
        parentMenu.setChildren(chlidMenus);//假设 SysMenu实体类有children字段属性子菜单
        return parentMenu;
    }
} 