package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.pojo.OldStu;
import com.zhentao.service.OldStuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/oldStu")
public class OldStuController {

    @Autowired
    private OldStuService oldStuService;

    @PostMapping("/findPage")
    public Page<OldStu> findPage(@RequestBody OldStu oldStu){
        Page<OldStu> page = new Page<>(oldStu.getPageNum(), oldStu.getPageSize());
        LambdaQueryWrapper<OldStu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OldStu::getDelFlag, 0);
        return oldStuService.page(page, wrapper);
    }

}
