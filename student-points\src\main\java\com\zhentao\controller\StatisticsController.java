package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsRecord;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.PointsRecordService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计分析控制器
 */
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    @Autowired
    private EduClassService eduClassService;
    
    @Autowired
    private EduStudentService eduStudentService;
    
    @Autowired
    private PointsRecordService pointsRecordService;
    
    /**
     * 获取班级积分概览
     */
    @GetMapping("/class-overview")
    public Result getClassPointsOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();
            
            // 获取所有班级
            List<EduClass> classes = eduClassService.list();
            overview.put("totalClasses", classes.size());
            
            // 计算所有班级的平均积分
            double totalAvgPoints = 0;
            String highestClassName = "";
            double highestClassPoints = 0;
            String lowestClassName = "";
            double lowestClassPoints = Double.MAX_VALUE;
            
            for (EduClass eduClass : classes) {
                // 获取班级学生
                LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(EduStudent::getClassId, eduClass.getClassId());
                List<EduStudent> students = eduStudentService.list(wrapper);
                
                if (!students.isEmpty()) {
                    // 计算班级平均积分
                    double avgPoints = students.stream()
                        .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                        .average()
                        .orElse(0);
                    
                    totalAvgPoints += avgPoints;
                    
                    // 更新最高/最低积分班级
                    if (avgPoints > highestClassPoints) {
                        highestClassPoints = avgPoints;
                        highestClassName = eduClass.getClassName();
                    }
                    
                    if (avgPoints < lowestClassPoints) {
                        lowestClassPoints = avgPoints;
                        lowestClassName = eduClass.getClassName();
                    }
                }
            }
            
            // 计算全校平均积分
            double schoolAvgPoints = classes.isEmpty() ? 0 : totalAvgPoints / classes.size();
            
            overview.put("avgPoints", Math.round(schoolAvgPoints * 10) / 10.0);
            overview.put("highestPoints", Math.round(highestClassPoints * 10) / 10.0);
            overview.put("highestClassName", highestClassName);
            overview.put("lowestPoints", Math.round(lowestClassPoints * 10) / 10.0);
            overview.put("lowestClassName", lowestClassName);
            
            return Result.OK(overview);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级积分概览失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取班级积分排名
     */
    @GetMapping("/class-rankings")
    public Result getClassRankings() {
        try {
            // 获取所有班级
            List<EduClass> classes = eduClassService.list();
            List<Map<String, Object>> rankings = new ArrayList<>();
            
            // 计算每个班级的平均积分
            for (EduClass eduClass : classes) {
                // 获取班级学生
                LambdaQueryWrapper<EduStudent> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(EduStudent::getClassId, eduClass.getClassId());
                List<EduStudent> students = eduStudentService.list(wrapper);
                
                if (!students.isEmpty()) {
                    // 计算班级平均积分
                    double avgPoints = students.stream()
                        .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                        .average()
                        .orElse(0);
                    
                    // 计算班级及格率（假设60分为及格线）
                    long passCount = students.stream()
                        .filter(student -> student.getPoints() != null && student.getPoints() >= 60)
                        .count();
                    double passRate = students.isEmpty() ? 0 : (double) passCount / students.size() * 100;
                    
                    // 计算最高分和最低分
                    int highestPoints = students.stream()
                        .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                        .max()
                        .orElse(0);
                    
                    int lowestPoints = students.stream()
                        .filter(student -> student.getPoints() != null && student.getPoints() > 0)
                        .mapToInt(EduStudent::getPoints)
                        .min()
                        .orElse(0);
                    
                    Map<String, Object> classRank = new HashMap<>();
                    classRank.put("classId", eduClass.getClassId());
                    classRank.put("className", eduClass.getClassName());
                    classRank.put("totalStudents", students.size());
                    classRank.put("avgPoints", Math.round(avgPoints * 10) / 10.0);
                    classRank.put("highestPoints", highestPoints);
                    classRank.put("lowestPoints", lowestPoints);
                    classRank.put("passRate", Math.round(passRate * 10) / 10.0);
                    
                    rankings.add(classRank);
                }
            }
            
            // 按平均积分降序排序
            rankings.sort((a, b) -> {
                Double pointsA = (Double) a.get("avgPoints");
                Double pointsB = (Double) b.get("avgPoints");
                return pointsB.compareTo(pointsA);
            });
            
            // 添加排名
            for (int i = 0; i < rankings.size(); i++) {
                rankings.get(i).put("rank", i + 1);
                // 模拟上月排名（实际应从数据库获取历史数据）
                rankings.get(i).put("lastMonthRank", Math.min(rankings.size(), Math.max(1, i + 1 + (int)(Math.random() * 3 - 1))));
            }
            
            return Result.OK(rankings);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级积分排名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取班级积分趋势
     */
    @GetMapping("/class-trend")
    public Result getClassPointsTrend(@RequestParam(required = false) List<Integer> classIds) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<String> months = new ArrayList<>();
            
            // 生成最近12个月的月份列表
            Calendar cal = Calendar.getInstance();
            SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
            
            for (int i = 11; i >= 0; i--) {
                cal.setTime(new Date());
                cal.add(Calendar.MONTH, -i);
                months.add(monthFormat.format(cal.getTime()));
            }
            
            result.put("months", months);
            
            List<Map<String, Object>> seriesList = new ArrayList<>();
            
            // 如果没有指定班级ID，则获取排名前3的班级
            if (classIds == null || classIds.isEmpty()) {
                // 获取班级排名
                Result rankingsResult = getClassRankings();
                if (rankingsResult.getCode() == 200) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> rankings = (List<Map<String, Object>>) rankingsResult.getData();
                    classIds = rankings.stream()
                        .limit(3)
                        .map(rank -> (Integer) rank.get("classId"))
                        .collect(Collectors.toList());
                }
            }
            
            // 获取指定班级的趋势数据
            for (Integer classId : classIds) {
                EduClass eduClass = eduClassService.getById(classId);
                if (eduClass != null) {
                    Map<String, Object> series = new HashMap<>();
                    series.put("name", eduClass.getClassName());
                    
                    // 模拟每月积分数据（实际应从数据库获取历史数据）
                    List<Double> data = new ArrayList<>();
                    double basePoints = 60 + Math.random() * 20;
                    
                    for (int i = 0; i < 12; i++) {
                        basePoints += Math.random() * 5 - 1;
                        data.add(Math.round(basePoints * 10) / 10.0);
                    }
                    
                    series.put("data", data);
                    seriesList.add(series);
                }
            }
            
            result.put("series", seriesList);
            
            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级积分趋势失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取班级积分分布
     */
    @GetMapping("/class-distribution")
    public Result getClassPointsDistribution() {
        try {
            // 获取所有学生
            List<EduStudent> students = eduStudentService.list();
            
            // 定义积分区间
            String[] ranges = {"90分以上", "80-90分", "70-80分", "60-70分", "60分以下"};
            int[] counts = new int[ranges.length];
            
            // 统计各区间学生数量
            for (EduStudent student : students) {
                Integer points = student.getPoints();
                if (points != null) {
                    if (points >= 90) {
                        counts[0]++;
                    } else if (points >= 80) {
                        counts[1]++;
                    } else if (points >= 70) {
                        counts[2]++;
                    } else if (points >= 60) {
                        counts[3]++;
                    } else {
                        counts[4]++;
                    }
                }
            }
            
            // 构建返回数据
            List<Map<String, Object>> distribution = new ArrayList<>();
            for (int i = 0; i < ranges.length; i++) {
                Map<String, Object> item = new HashMap<>();
                item.put("name", ranges[i]);
                item.put("value", counts[i]);
                distribution.add(item);
            }
            
            return Result.OK(distribution);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级积分分布失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取班级积分详情列表
     */
    @GetMapping("/class-details")
    public Result getClassPointsDetails(@RequestParam(required = false) String className) {
        try {
            // 获取班级列表
            LambdaQueryWrapper<EduClass> classWrapper = new LambdaQueryWrapper<>();
            if (className != null && !className.isEmpty()) {
                classWrapper.like(EduClass::getClassName, className);
            }
            List<EduClass> classes = eduClassService.list(classWrapper);
            
            // 获取班级详情
            List<Map<String, Object>> details = new ArrayList<>();
            for (EduClass eduClass : classes) {
                // 获取班级学生
                LambdaQueryWrapper<EduStudent> studentWrapper = new LambdaQueryWrapper<>();
                studentWrapper.eq(EduStudent::getClassId, eduClass.getClassId());
                List<EduStudent> students = eduStudentService.list(studentWrapper);
                
                if (!students.isEmpty()) {
                    // 计算班级平均积分
                    double avgPoints = students.stream()
                        .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                        .average()
                        .orElse(0);
                    
                    // 计算班级及格率
                    long passCount = students.stream()
                        .filter(student -> student.getPoints() != null && student.getPoints() >= 60)
                        .count();
                    double passRate = students.isEmpty() ? 0 : (double) passCount / students.size() * 100;
                    
                    // 计算最高分和最低分
                    int highestPoints = students.stream()
                        .mapToInt(student -> student.getPoints() != null ? student.getPoints() : 0)
                        .max()
                        .orElse(0);
                    
                    int lowestPoints = students.stream()
                        .filter(student -> student.getPoints() != null && student.getPoints() > 0)
                        .mapToInt(EduStudent::getPoints)
                        .min()
                        .orElse(0);
                    
                    Map<String, Object> detail = new HashMap<>();
                    detail.put("id", eduClass.getClassId());
                    detail.put("className", eduClass.getClassName());
                    detail.put("totalStudents", students.size());
                    detail.put("avgPoints", Math.round(avgPoints * 10) / 10.0);
                    detail.put("highestPoints", highestPoints);
                    detail.put("lowestPoints", lowestPoints);
                    detail.put("passRate", Math.round(passRate * 10) / 10.0);
                    
                    details.add(detail);
                }
            }
            
            // 按平均积分降序排序
            details.sort((a, b) -> {
                Double pointsA = (Double) a.get("avgPoints");
                Double pointsB = (Double) b.get("avgPoints");
                return pointsB.compareTo(pointsA);
            });
            
            // 添加排名
            for (int i = 0; i < details.size(); i++) {
                details.get(i).put("rank", i + 1);
                // 模拟上月排名（实际应从数据库获取历史数据）
                details.get(i).put("lastMonthRank", Math.min(details.size(), Math.max(1, i + 1 + (int)(Math.random() * 3 - 1))));
            }
            
            return Result.OK(details);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取班级积分详情失败: " + e.getMessage());
        }
    }
} 