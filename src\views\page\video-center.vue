<template>
  <div class="video-center-page">
    <h2 class="video-title">视频中心</h2>
    <div class="video-intro">
      这里汇聚了振涛教育云计算学院的优质教学视频、活动回顾、技术讲座等内容，助你随时随地学习与提升。
    </div>
    <div class="video-list">
      <div class="video-card" v-for="(item, idx) in videos" :key="idx">
        <div class="video-thumb">
          <video :src="item.src" controls poster="/images/zhentao/yun1.png"></video>
        </div>
        <div class="video-info">
          <h3>{{ item.title }}</h3>
          <p>{{ item.desc }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const videos = [
  {
    title: '云计算技术公开课',
    desc: '深入浅出讲解云计算核心技术，适合初学者和进阶者。',
    src: '/videos/demo1.mp4'
  },
  {
    title: 'AI编程实战演示',
    desc: 'AI+编程项目实录，带你体验真实开发流程。',
    src: '/videos/demo2.mp4'
  },
  {
    title: '学员风采与就业分享',
    desc: '优秀学员经验分享，助力你的成长与就业。',
    src: '/videos/demo3.mp4'
  },
  {
    title: '企业实训项目回顾',
    desc: '真实企业项目实训过程全记录。',
    src: '/videos/demo4.mp4'
  }
]
</script>

<style scoped>
.video-center-page {
  max-width: 1100px;
  margin: 40px auto 60px auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(60,154,240,0.07);
  padding: 40px 32px 32px 32px;
}
.video-title {
  text-align: center;
  font-size: 2.2rem;
  color: #1a4f8c;
  margin-bottom: 24px;
  font-weight: 700;
  letter-spacing: 2px;
}
.video-intro {
  text-align: center;
  font-size: 1.1rem;
  color: #444;
  margin-bottom: 28px;
}
.video-list {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
  justify-content: center;
}
.video-card {
  width: 320px;
  background: #f5f8fc;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(60,154,240,0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 18px;
  transition: box-shadow 0.2s, transform 0.2s;
}
.video-card:hover {
  box-shadow: 0 8px 24px rgba(60,154,240,0.16);
  transform: translateY(-4px) scale(1.03);
}
.video-thumb {
  width: 100%;
  height: 180px;
  background: #eaf3ff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-thumb video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-bottom: 1px solid #e6eaf0;
  background: #000;
}
.video-info {
  padding: 18px 14px 0 14px;
  text-align: center;
}
.video-info h3 {
  font-size: 1.1rem;
  color: #1989fa;
  margin-bottom: 8px;
  font-weight: 600;
}
.video-info p {
  font-size: 0.98rem;
  color: #444;
  margin-bottom: 14px;
}
</style> 