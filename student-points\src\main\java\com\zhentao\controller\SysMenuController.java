package com.zhentao.controller;

import com.zhentao.pojo.SysMenu;
import com.zhentao.service.SysMenuService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/sysMenu")
public class SysMenuController {
    @Autowired
    private SysMenuService sysMenuService;
    @PostMapping("/findAllMenu")
    public Result findAllMenu(){
        return sysMenuService.findAllMenu();
    }
}
