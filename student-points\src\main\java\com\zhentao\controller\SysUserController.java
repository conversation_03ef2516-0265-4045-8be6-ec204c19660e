package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.dto.system.system.PasswordDto;
import com.zhentao.dto.system.system.SysUserDto;
import com.zhentao.pojo.SysUser;
import com.zhentao.service.SysUserService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 系统用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/sysUser")
public class SysUserController {
    @Autowired
    private SysUserService sysUserService;
    /**
     * 获取用户分页
     */
    @PostMapping("/getUserPage")
    public Page<SysUser> getUserPage(@RequestBody SysUserDto sysUserDto) {
        return sysUserService.getUserPage(sysUserDto);
    }
    /**
     * 添加用户与角色
     */
    @PostMapping("/addUserRole")
    public Result addUserRole(@RequestBody SysUser sysUser) {
        return sysUserService.addUserRole(sysUser);
    }
    /**
     * 修改用户与角色
     */
    @PostMapping("/updateUserRole")
    public Result updateUserRole(@RequestBody SysUser sysUser) {
        return sysUserService.updateUserRole(sysUser);
    }
    /**
     * 删除用户
     */
    @PostMapping("/deleteUser")
    public Result deleteUser(@RequestParam("userId") Integer userId) {
        return sysUserService.deleteUserRole(userId);
    }
    /**
     * 修改用户状态
     */
    @PostMapping("/updateUserStatus")
    public Result updateUserStatus(@RequestParam("userId")Integer userId,@RequestParam("status") Integer status) {
        return sysUserService.updateUserStatus(userId, status);
    }
    /**
     * 重置用户密码
     */
    @PostMapping("/resetUserPassword")
    public Result resetUserPassword(Integer userId) {
        return sysUserService.resetUserPassword(userId);
    }

    /**
     * 根据用户类型获取用户列表
     * @param userType 用户类型（1-讲师，2-导员，3-阶段主任，4-院长，9-超级管理员）
     * @return 用户列表
     */
    @GetMapping("/getUsersByType")
    public Result getUsersByType(@RequestParam(required = false) Integer userType) {
        try {
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();

            if (userType != null) {
                wrapper.eq(SysUser::getUserType, userType);
            }

            // 只查询正常状态的用户
            wrapper.eq(SysUser::getStatus, 0);
            wrapper.eq(SysUser::getDelFlag, 0);

            // 按姓名排序
            wrapper.orderByAsc(SysUser::getRealName);

            List<SysUser> users = sysUserService.list(wrapper);

            // 清理敏感信息
            for (SysUser user : users) {
                user.setPassword(null);
            }

            return Result.OK(users);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取用户列表失败：" + e.getMessage());
        }
    }
    /**
     * 修改用户信息
     */
    @PostMapping("/updateUserInfo")
    public Result updateUserInfo(@RequestBody SysUser sysUser) {
        return sysUserService.updateUserInfo(sysUser);
    }
    /**
     * 修改用户密码
     */
    @PostMapping("/updateUserPassword")
    public Result updateUserPassword(@RequestBody PasswordDto passwordDto) {
        return sysUserService.updateUserPassword(passwordDto);
    }
    /**
     * 获取用户详情
     */
    @GetMapping("/getUserDetail")
    public Result getUserDetail() {
        return sysUserService.getUserDetail();
    }
}
