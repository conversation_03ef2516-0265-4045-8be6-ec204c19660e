<template>
  <div class="points-rules-page">
    <button class="back-home-btn" @click="goHome">返回首页</button>
    <h2 class="points-title">积分规则</h2>
    <div class="points-content">
      <pre class="points-pre">
云计算学院学生综合积分管理制度
第一章总则
第一条目的
为全面贯彻党的教育方针，落实立德树人根本任务，加强学生日常行为规范管理，引导学生树立正确价值观，培养德智体美劳全面发展的高素质人才，特制定本学生综合积分管理制度。通过积分量化学生在校期间的综合表现，激励学生积极进取，规范自身行为，营造良好的学风、校风。
第二条适用范围
本制度适用于云计算学院全体在校学生，涵盖学生在思想道德、学业表现、社会实践、文体活动、日常行为等方面的综合评价。
第三条积分设定
学生综合积分以 100 分为基础分，实行加减分制，上不封顶，下不保底。积分将作为学生评优评先、升班、升实训等重要参考依据。
第二章积分构成
学生综合积分由思想道德积分、学业表现积分、社会实践积分、文体活动积分和日常行为积分五部分构成，各部分积分根据学生实际表现进行加减。
第三章加分规则
1.积极争当本班级班委，并切实履行了班委义务，未与他人发生冲突，教学周期结束，全班对班委进行打分，满分10分，平均分达到9.5分及以上者，教学周期结束加5分，达到8-9.5分者包含8分，教学周期结束加3分，达到6-8分者，教学周期结束加2分，6分以下，教学周期结束加1分；
2.积极参与学生会工作，加入学生会，并切实履行了成员义务，未收到学生投诉，管理过程中未与其他学生发生肢体冲突相互辱骂等行为，并严格按照学院规章制度，没有徇私舞弊，没有特权行为且积极参与学院以及学生会主席以及部长安排的工作，本教学周期结束加3分；连续任职且表现优秀，从第2个月开始每个教学月加2分，学生会主席恪于职守，表现优秀没有收到举报等行为，连续任职2个月及以上者加10分，连续任职6个月以及上且表现优异者再加15分；部长连续任职两个月及以上且表现优秀加8分，连续任职6个月及以上且表现优异追加10分；
3.每个教学周期开课第3天进行月度卫生大检查，宿舍被督察评定为标准间，全宿舍每人加3分；评定为合格间（6分及以上的宿舍），全宿舍每人加1分；
4.卫生大检查中参与厕所卫生打扫并由督察打分取得10分，每人加3分；参与楼道，楼梯等卫生区打扫的且取得10分的，每人加3分；
5.积极参与院内活动，争当主持人，以及上台演讲者，综合积分加3分；
6.每个教学周期五率一值排名进全校前20名的班级，全班同学每人加2分，班委每人追加2分；
7.每个教学月0违纪的班级，每人综合积分加2分，班委追加2分；
8.每个教学月正课出勤100%，自习课出勤100%且晚自习出勤100%的班级每人加2分，班委追加2分；
9.每个教学周期卫生合格率100%的班级，班级每人加2分，班委追加2分；
10.每个教学周期结束，个人没有任何违纪，请假，旷课，迟到，晨会未到，没有休学，没有消极言论等情况，教学周期结束加2分；
11.本教学周期所有作业按时按质量完成，经学委，技术经理核实，讲师核实，教学周期结束加2分；
12.专高三至专高六项目完成度高，答辩优秀的个人，经项目经理评选，经全班认可的优秀个人，每个教学周期评选0-10名，每人加5分；
13.每日的日视频汇报表达优秀的学生，每个月讲师筛选出0~10人，拿出优质视频为证，一人加2分；
14.主动帮助同学解决思想困惑或矛盾纠纷，经同学以及讲师和辅导员证实，每次加 2 分，每学期累计不超过 5 分；在校园内传播正能量，引导同学树立正确价值观，受到师生广泛认可，加 3~5 分。
15.积极参与学院宣传活动，撰写高质量心得体会并被学院视频号引用，公众号推送，每次加 2 分，每学期累计不超过 8 分。
16.协助市场部老师进行招生宣讲等工作，且表现优秀者，每次加2分，一个教学周期的上限为5分；
17.辅助对外宣传云计算学院，并成功吸引学生报考云计算学院，并且有聊天证明的，一次加5分；
18.有创新意识，创意想法，上报给学院并成功将项目开发成功，并上线运行的最后用户投入使用的，一次加20分；
19.协助老师参与院内或者校内集体活动，视情况可由学生本人向讲师或者辅导员申请，再由老师向院长申请，可视贡献程度大小加分1-10分不等；
20.每个教学周期结束讲师和辅导员视学生日常行为表现分别有5个名额申请给学生加分1-2分；
第四章减分规则
1.无故不参加晨会，未到者一次扣2分，晨会迟到，一次扣1分；请假扣0.5分；
2.日常教室违纪或者宿舍违纪，根据校级综合积分管理制度，每次扣除相应的积分；
3.自习课或者晚自习迟到扣2分，自习课未经申请进行串班扣5分（专高以及实训班级进班进行演讲或者讲项目除外），自习课走廊或者校园游荡扣5分；
4.假期返校未按规定时间到校者，请假扣5分，乘坐大巴车晚点扣5分，其他扣10分；极其特殊情况除外（例如下冰雹，洪水，台风，暴雨等极端恶劣天气）；
5.宿舍三件套不合格者，每个教学周期扣除综合积分5分；
6.无故私自离校者，一次扣除综合积分30分；旷寝一次扣除综合积分10分；
7.打架斗殴，视情节严重程度，扣除综合积分5~20分不等；严重者休学或者退学处理；
8.一个教学周期出现5次及以上宿舍卫生不合格者，扣除综合积分10分；
9.辱骂他人，在教学区域说脏话，一次扣除综合积分5分；
10.一个教学周期未参与过班级以及学院卫生值日者，扣除综合积分2分；
11.公然顶撞老师者，扣除综合积分10分；
12.学生之间发生吵架等争执，视情节严重扣除1~10分；
13.欺瞒老师，被查出者扣除综合积分5分；
第五章积分管理与应用
1.各班级辅导员负责本班学生积分的日常记录与统计工作。
2.每个教学月结束进行综合积分公示，如有异议，可进行反馈；
3.综合积分达到140分者方可升入实训，否则不允许升实训；本条制度适用于2409A班级及以后的所有班级；
4.现有的专高四综合积分达到120，专高五综合积分达到110，专高六综合积分100分方可升班；
第六章 附则​
第一条 本制度自发布之日起施行，最终解释权归云计算学院所有。​
第二条 本制度将根据学校发展和实际情况适时修订完善。​


                                                                            日期：2025.05.08
      </pre>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();
function goHome() {
  router.push({ path: '/' });
}
</script>

<style scoped>
.points-rules-page {
  max-width: 1100px;
  margin: 40px auto 60px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(60,154,240,0.10);
  padding: 48px 36px 36px 36px;
  position: relative;
}
.back-home-btn {
  position: absolute;
  left: 36px;
  top: 24px;
  background: linear-gradient(90deg, #3c9af0 0%, #1a4f8c 100%);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(60,154,240,0.13);
  transition: background 0.3s, transform 0.2s, box-shadow 0.2s;
  z-index: 2;
}
.back-home-btn:hover {
  background: linear-gradient(90deg, #1a4f8c 0%, #3c9af0 100%);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 6px 18px rgba(60,154,240,0.18);
}
.points-title {
  text-align: center;
  font-size: 2.4rem;
  color: #1a4f8c;
  margin-bottom: 28px;
  font-weight: 800;
  letter-spacing: 2.5px;
  margin-top: 18px;
}
.points-content {
  font-size: 1.08rem;
  color: #444;
  line-height: 1.85;
}
.points-pre {
  background: #f5f8fc;
  border-radius: 10px;
  padding: 22px 18px;
  font-size: 1.02rem;
  color: #222;
  white-space: pre-wrap;
  word-break: break-all;
  overflow-x: auto;
  box-shadow: 0 2px 8px rgba(60,154,240,0.06);
}
</style> 