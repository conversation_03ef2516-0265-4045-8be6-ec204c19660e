<template>
  <div class="batch-import-container">
    <el-card class="upload-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>批量导入学生</h3>
          <el-button type="primary" @click="downloadTemplate">下载模板</el-button>
        </div>
      </template>
      
      <div class="upload-area">
        <el-upload
          class="upload-component"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          accept=".xlsx,.xls,.csv"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请上传 Excel 或 CSV 格式文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button type="primary" @click="parseFile" :loading="parsing">解析文件</el-button>
          <el-button @click="clearFile">清除文件</el-button>
        </div>
      </div>
      
      <div class="import-steps">
        <el-steps :active="activeStep" finish-status="success">
          <el-step title="上传文件" description="上传Excel或CSV文件"></el-step>
          <el-step title="预览数据" description="检查导入数据"></el-step>
          <el-step title="确认导入" description="导入到系统"></el-step>
          <el-step title="导入完成" description="查看导入结果"></el-step>
        </el-steps>
      </div>
    </el-card>
    
    <el-card class="preview-card" shadow="hover" v-if="previewData.length > 0">
      <template #header>
        <div class="card-header">
          <h3>数据预览</h3>
          <div class="header-actions">
            <el-tag :type="getValidTagType">有效数据: {{ validCount }}/{{ previewData.length }}</el-tag>
            <el-button type="primary" @click="importData" :disabled="validCount === 0" :loading="importing">开始导入</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="previewData" style="width: 100%" border stripe>
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="studentNo" label="学号" width="120">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.studentNo }">
              {{ scope.row.studentNo }}
              <el-tooltip v-if="scope.row.errors?.studentNo" :content="scope.row.errors.studentNo" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="姓名" width="100">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.realName }">
              {{ scope.row.realName }}
              <el-tooltip v-if="scope.row.errors?.realName" :content="scope.row.errors.realName" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.gender }">
              {{ scope.row.gender === 1 ? '男' : (scope.row.gender === 0 ? '女' : scope.row.gender) }}
              <el-tooltip v-if="scope.row.errors?.gender" :content="scope.row.errors.gender" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="className" label="班级" min-width="150">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.className }">
              {{ scope.row.className }}
              <el-tooltip v-if="scope.row.errors?.className" :content="scope.row.errors.className" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.phone }">
              {{ scope.row.phone }}
              <el-tooltip v-if="scope.row.errors?.phone" :content="scope.row.errors.phone" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180">
          <template #default="scope">
            <div :class="{ 'error-cell': scope.row.errors?.email }">
              {{ scope.row.email }}
              <el-tooltip v-if="scope.row.errors?.email" :content="scope.row.errors.email" placement="top">
                <el-icon class="error-icon"><warning /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isValid ? 'success' : 'danger'">
              {{ scope.row.isValid ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-actions">
        <el-button @click="activeStep = 0">返回上传</el-button>
        <el-button type="primary" @click="importData" :disabled="validCount === 0" :loading="importing">开始导入</el-button>
      </div>
    </el-card>
    
    <el-card class="result-card" shadow="hover" v-if="importResult.show">
      <template #header>
        <div class="card-header">
          <h3>导入结果</h3>
        </div>
      </template>
      
      <el-result
        :icon="importResult.success ? 'success' : 'error'"
        :title="importResult.title"
        :sub-title="importResult.message"
      >
        <template #extra>
          <el-button type="primary" @click="resetImport">继续导入</el-button>
          <el-button @click="goToStudentList">查看学生列表</el-button>
        </template>
      </el-result>
      
      <div class="import-summary" v-if="importResult.success">
        <el-descriptions title="导入统计" :column="2" border>
          <el-descriptions-item label="总数据条数">{{ importResult.totalCount }}</el-descriptions-item>
          <el-descriptions-item label="成功导入">{{ importResult.successCount }}</el-descriptions-item>
          <el-descriptions-item label="失败条数">{{ importResult.failCount }}</el-descriptions-item>
          <el-descriptions-item label="导入用时">{{ importResult.time }}秒</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="error-list" v-if="importResult.errors && importResult.errors.length > 0">
        <h4>导入错误详情</h4>
        <el-table :data="importResult.errors" style="width: 100%" border stripe>
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column prop="row" label="行号" width="80" />
          <el-table-column prop="id" label="学号" width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="errorMsg" label="错误原因" min-width="300" />
        </el-table>
      </div>
    </el-card>
    
    <el-card class="help-card" shadow="hover" v-if="!previewData.length && !importResult.show">
      <template #header>
        <div class="card-header">
          <h3>导入说明</h3>
        </div>
      </template>
      
      <div class="help-content">
        <h4>批量导入学生信息说明</h4>
        <p>1. 下载模板：点击"下载模板"按钮获取标准Excel导入模板</p>
        <p>2. 填写数据：按照模板格式填写学生信息</p>
        <p>3. 上传文件：将填写好的Excel文件上传</p>
        <p>4. 预览数据：系统会自动检查数据格式，标记错误数据</p>
        <p>5. 确认导入：确认无误后点击"开始导入"按钮</p>
        
        <h4 class="mt-20">数据要求</h4>
        <el-table :data="fieldRequirements" style="width: 100%" border>
          <el-table-column prop="field" label="字段" width="120" />
          <el-table-column prop="required" label="是否必填" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.required ? 'danger' : 'info'">
                {{ scope.row.required ? '必填' : '选填' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="说明" min-width="300" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Warning } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import request from '@/request'
import axios from 'axios' // Added axios import

const router = useRouter()

// 文件上传相关
const fileList = ref([])
const parsing = ref(false)
const importing = ref(false)
const activeStep = ref(0)
const previewData = ref([])

// 导入结果
const importResult = reactive({
  show: false,
  success: false,
  title: '',
  message: '',
  totalCount: 0,
  successCount: 0,
  failCount: 0,
  time: 0,
  errors: []
})

// 班级选项
const classOptions = ref([])

// 获取班级选项
const fetchClassOptions = async () => {
  try {
    // 正在获取班级数据
    const res = await request({
      url: '/edu-student/getClassOptions',
      method: 'get'
    });
    
    if (res.data && res.data.code === 200) {
      classOptions.value = res.data.data || [];
    } else {
      classOptions.value = [];
      console.warn('获取班级选项失败:', res.data);
    }
  } catch (error) {
    console.error('获取班级选项失败:', error);
    classOptions.value = [];
  }
}

// 在组件挂载时获取班级选项
onMounted(() => {
  fetchClassOptions();
});

// 字段要求说明
const fieldRequirements = [
  { field: '学号', required: true, description: '必须为7-20位字符，且不能重复' },
  { field: '姓名', required: true, description: '2-20个字符' },
  { field: '性别', required: true, description: '只能填写"男"或"女"' },
  { field: '班级', required: true, description: '必须是系统中已存在的班级' },
  { field: '联系电话', required: false, description: '11位手机号码格式' },
  { field: '邮箱', required: false, description: '有效的邮箱格式' },
  { field: '备注', required: false, description: '最多500个字符' }
]

// 计算有效数据数量
const validCount = computed(() => {
  return previewData.value.filter(item => item.isValid).length
})

// 获取有效数据标签类型
const getValidTagType = computed(() => {
  if (validCount.value === 0) return 'danger'
  if (validCount.value < previewData.value.length) return 'warning'
  return 'success'
})

// 处理文件变更
const handleFileChange = (file) => {
  fileList.value = [file]
  // 重置预览数据和导入结果
  previewData.value = []
  importResult.show = false
}

// 清除文件
const clearFile = () => {
  fileList.value = []
  previewData.value = []
  activeStep.value = 0
  importResult.show = false
}

// 下载模板
const downloadTemplate = async () => {
  try {
    ElMessage.info('正在下载模板，请稍候...')
    
    // 获取token
    const token = localStorage.getItem("Authorization")
    if (!token) {
      ElMessage.error('未登录或登录已过期，请重新登录')
      return
    }
    
    // 直接从API下载Excel文件
    const response = await axios({
      url: '/api/edu-student/downloadTemplate',
      method: 'get',
      headers: {
        'Authorization': token
      },
      responseType: 'blob'
    })
    
    // 检查响应类型和内容
    const contentType = response.headers['content-type']
    
    // 如果返回的是JSON（通常是错误信息），解析并显示
    if (contentType && contentType.includes('application/json')) {
      const reader = new FileReader()
      reader.onload = () => {
        try {
          const jsonResponse = JSON.parse(reader.result)
          ElMessage.error('下载失败: ' + (jsonResponse.message || '未知错误'))
          console.error('下载模板失败:', jsonResponse)
        } catch (e) {
          ElMessage.error('下载失败，服务器返回了无效的响应')
          console.error('解析JSON响应失败:', e)
        }
      }
      reader.readAsText(new Blob([response.data]))
      return
    }
    
    // 检查是否是空响应或非Excel文件
    if (!response.data || response.data.size === 0 || 
        (contentType && !contentType.includes('spreadsheetml') && !contentType.includes('ms-excel'))) {
      console.error('服务器返回了非Excel文件:', contentType)
      ElMessage.error('服务器返回了非Excel文件，使用本地模板')
      createSimpleTemplate()
      return
    }
    
    // 处理Excel文件下载
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    // 获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = '学生导入模板.xlsx'
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=(.+)\.xlsx/)
      if (filenameMatch && filenameMatch.length > 1) {
        filename = decodeURIComponent(filenameMatch[1]) + '.xlsx'
      }
    }
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败: ' + (error.message || '未知错误'))
    
    // 如果API调用完全失败，提供一个本地备用模板
    createSimpleTemplate()
  }
}

// 创建简单的模板（当API不可用时的备用方案）
const createSimpleTemplate = () => {
  // 获取班级列表作为参考
  let sampleClassName = '2409A';  // 默认使用已知存在的班级
  let classNamesList = [];
  
  if (classOptions.value && classOptions.value.length > 0) {
    sampleClassName = classOptions.value[0].className;
    classNamesList = classOptions.value.map(c => c.className);
  }
  
  // 创建工作簿和工作表
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet([]);
  
  // 设置列宽
  const colWidths = [15, 10, 6, 15, 15, 25, 30];
  worksheet['!cols'] = colWidths.map(width => ({ width }));
  
  // 添加表头
  const headers = ['学号', '姓名', '性别', '班级', '联系电话', '邮箱', '备注'];
  XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'A1' });
  
  // 添加必填行
  const requiredFields = ['必填', '必填', '必填', '必填', '选填', '选填', '选填'];
  XLSX.utils.sheet_add_aoa(worksheet, [requiredFields], { origin: 'A2' });
  
  // 添加字段说明行
  const descriptions = [
    '必须为7-20位字符，且不能重复', 
    '2-20个字符', 
    '男/女', 
    '必须是系统中已存在的班级', 
    '11位手机号码格式', 
    '有效的邮箱格式', 
    '最多500个字符'
  ];
  XLSX.utils.sheet_add_aoa(worksheet, [descriptions], { origin: 'A3' });
  
  // 添加示例数据行
  const exampleData = [
    '20210501001', 
    '张三', 
    '男', 
    sampleClassName, 
    '13800138000', 
    '<EMAIL>', 
    '示例数据'
  ];
  XLSX.utils.sheet_add_aoa(worksheet, [exampleData], { origin: 'A4' });
  
  // 添加空行
  XLSX.utils.sheet_add_aoa(worksheet, [['']], { origin: 'A5' });
  
  // 添加批量导入说明标题
  XLSX.utils.sheet_add_aoa(worksheet, [['批量导入学生信息说明']], { origin: 'A6' });
  
  // 添加导入步骤说明
  const steps = [
    '1. 下载模板：点击"下载模板"按钮获取标准Excel导入模板',
    '2. 填写数据：按照模板格式填写学生信息',
    '3. 上传文件：将填写好的Excel文件上传',
    '4. 预览数据：系统会自动检查数据格式，标记错误数据',
    '5. 确认导入：确认无误后点击"开始导入"按钮'
  ];
  
  let rowIndex = 7;
  steps.forEach(step => {
    XLSX.utils.sheet_add_aoa(worksheet, [[step]], { origin: `A${rowIndex}` });
    rowIndex++;
  });
  
  // 添加注意事项标题
  XLSX.utils.sheet_add_aoa(worksheet, [['注意事项：']], { origin: `A${rowIndex + 1}` });
  rowIndex += 2;
  
  // 添加注意事项
  const notes = [
    '1. 学号必须唯一，系统会自动检查重复学号',
    '2. 性别只能填写"男"或"女"',
    '3. 班级必须是系统中已存在的班级',
    '4. 请确保Excel文件格式正确，避免使用特殊字符'
  ];
  
  notes.forEach(note => {
    XLSX.utils.sheet_add_aoa(worksheet, [[note]], { origin: `A${rowIndex}` });
    rowIndex++;
  });
  
  // 添加班级列表标题
  XLSX.utils.sheet_add_aoa(worksheet, [['系统中已存在的班级列表：']], { origin: `A${rowIndex + 1}` });
  rowIndex += 2;
  
  // 添加班级列表
  if (classNamesList.length > 0) {
    // 每行最多4个班级
    const classesPerRow = 4;
    for (let i = 0; i < classNamesList.length; i += classesPerRow) {
      const classesRow = classNamesList.slice(i, i + classesPerRow);
      XLSX.utils.sheet_add_aoa(worksheet, [classesRow], { origin: `A${rowIndex}` });
      rowIndex++;
    }
  } else {
    XLSX.utils.sheet_add_aoa(worksheet, [['暂无班级数据']], { origin: `A${rowIndex}` });
  }
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '学生导入');
  
  // 写入文件并下载
  XLSX.writeFile(workbook, '学生导入模板.xlsx');
  
  ElMessage.success('已生成本地模板');
}

// 解析文件
const parseFile = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传文件');
    return;
  }
  
  parsing.value = true;
  activeStep.value = 1;
  
  const file = fileList.value[0].raw;
  
  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB');
    parsing.value = false;
    return;
  }
  
  // 更宽松的文件类型检查
  const fileName = file.name.toLowerCase();
  const validExcelExtensions = ['.xlsx', '.xls', '.csv', '.et', '.ett'];
  const hasValidExtension = validExcelExtensions.some(ext => fileName.endsWith(ext));
  
  if (!hasValidExtension) {
    ElMessage.error('请上传有效的Excel格式文件(xlsx/xls/csv)');
    parsing.value = false;
    return;
  }
  
  // 使用FileReader读取文件
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      // 解析Excel文件，使用更宽松的选项
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { 
        type: 'array',
        cellDates: true,
        cellNF: true,
        cellText: true,
        cellStyles: true,
        dateNF: 'yyyy-mm-dd'
      });
      
      if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new Error('无法识别的Excel文件格式');
      }
      
      // 获取第一个工作表
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      
      if (!worksheet) {
        throw new Error('工作表为空');
      }
      
      // 将工作表转换为JSON，设置更多选项以提高兼容性
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1,
        raw: false,
        defval: null
      });
      
      // 检查表头
      const headers = jsonData[0] || [];
      
      if (!headers || headers.length === 0) {
        throw new Error('表格为空或格式不正确，无法识别表头');
      }
      
      // 转换表头为统一格式，去除空格和统一大小写
      const normalizedHeaders = headers.map(h => typeof h === 'string' ? h.trim() : String(h));
      
      // 字段的所有可能表头
      const allHeadersMap = {
        'studentNo': ['学号', 'studentno', 'student_no', 'student no', 'id', '编号'],
        'realName': ['姓名', 'name', 'realname', 'real_name', 'real name', '学生姓名'],
        'gender': ['性别', 'gender', 'sex'],
        'className': ['班级', 'class', 'classname', 'class_name', 'class name'],
        'phone': ['联系电话', '手机号', '电话', 'phone', 'mobile'],
        'email': ['邮箱', '电子邮件', '邮件', 'email'],
        'remark': ['备注', '说明', 'remark', 'note']
      };
      // 建立字段名到列索引的映射
      const headerIndexMap = {};
      Object.keys(allHeadersMap).forEach(field => {
        const alternatives = allHeadersMap[field];
        const idx = normalizedHeaders.findIndex(h =>
          alternatives.some(name =>
            h.toLowerCase() === name.toLowerCase() ||
            h.toLowerCase().includes(name.toLowerCase())
          )
        );
        if (idx !== -1) headerIndexMap[field] = idx;
      });
      
      // 检查必填字段
      const requiredFields = ['studentNo', 'realName', 'gender', 'className'];
      const missingHeaders = requiredFields.filter(f => headerIndexMap[f] === undefined);
      if (missingHeaders.length > 0) {
        const zhMap = {studentNo:'学号',realName:'姓名',gender:'性别',className:'班级'};
        throw new Error(`表格缺少必要的列: ${missingHeaders.map(f=>zhMap[f]||f).join(', ')}`);
      }
      
      // 解析数据
      const parsedData = [];
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (!row || !row.length || !row[headerIndexMap['studentNo']]) continue; // 跳过空行或没有学号的行
        // 处理性别字段，确保只有"男"或"女"
        let genderValue = headerIndexMap.gender !== undefined ? row[headerIndexMap.gender] : null;
        let genderCode;
        if (typeof genderValue === 'string') {
          const normalizedGender = genderValue.trim().toLowerCase();
          if (normalizedGender === '男' || normalizedGender === 'male' || normalizedGender === '1' || normalizedGender === 'm') {
            genderCode = 1;
          } else if (normalizedGender === '女' || normalizedGender === 'female' || normalizedGender === '0' || normalizedGender === 'f') {
            genderCode = 0;
          } else {
            genderCode = null;
          }
        } else if (genderValue === 1 || genderValue === '1') {
          genderCode = 1;
        } else if (genderValue === 0 || genderValue === '0') {
          genderCode = 0;
        } else {
          genderCode = null;
        }
        const student = {
          studentNo: headerIndexMap.studentNo !== undefined ? row[headerIndexMap.studentNo] : null,
          realName: headerIndexMap.realName !== undefined ? row[headerIndexMap.realName] : null,
          gender: genderCode,
          rawGender: genderValue,
          className: headerIndexMap.className !== undefined ? row[headerIndexMap.className] : null,
          phone: headerIndexMap.phone !== undefined ? row[headerIndexMap.phone] : null,
          email: headerIndexMap.email !== undefined ? row[headerIndexMap.email] : null,
          remark: headerIndexMap.remark !== undefined ? row[headerIndexMap.remark] : null,
          isValid: true,
          errors: {}
        };
        parsedData.push(student);
      }
      previewData.value = parsedData;
      // 验证数据
      previewData.value.forEach(student => {
        const errors = validateStudentData(student);
        student.errors = Object.keys(errors).length > 0 ? errors : null;
        student.isValid = !student.errors;
      });
      // 检查是否有有效数据
      if (previewData.value.length === 0) {
        ElMessage.warning('没有找到有效的学生数据');
      } else {
        ElMessage.success(`成功解析 ${previewData.value.length} 条数据，其中有效数据 ${validCount.value} 条`);
      }
      activeStep.value = 2;
    } catch (error) {
      console.error('解析文件失败:', error);
      ElMessage.error('解析文件失败: ' + error.message);
      activeStep.value = 0;
    } finally {
      parsing.value = false;
    }
  };
  reader.onerror = () => {
    ElMessage.error('读取文件失败');
    parsing.value = false;
    activeStep.value = 0;
  };
  reader.readAsArrayBuffer(file);
}

// 验证学生数据
const validateStudentData = (student) => {
  const errors = {};
  
  // 验证学号
  if (!student.studentNo) {
    errors.studentNo = '学号不能为空';
  } else if (student.studentNo.length < 7 || student.studentNo.length > 20) {
    errors.studentNo = '学号长度必须为7-20位字符';
  }
  
  // 验证姓名
  if (!student.realName) {
    errors.realName = '姓名不能为空';
  } else if (student.realName.length < 2 || student.realName.length > 20) {
    errors.realName = '姓名长度必须为2-20个字符';
  }
  
  // 验证性别
  if (student.gender === null) {
    errors.gender = '性别不能为空';
  } else if (student.gender !== 0 && student.gender !== 1) {
    errors.gender = '性别必须是"男"或"女"';
  }
  
  // 验证班级
  if (!student.className) {
    errors.className = '班级不能为空';
  } else if (!classOptions.value.some(c => c.className === student.className)) {
    errors.className = '班级必须是系统中已存在的班级';
  }
  
  // 验证手机号（如果有）
  if (student.phone && !/^1[3-9]\d{9}$/.test(student.phone)) {
    errors.phone = '手机号格式不正确，应为11位数字';
  }
  
  // 验证邮箱（如果有）
  if (student.email && !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(student.email)) {
    errors.email = '邮箱格式不正确';
  }
  
  // 验证备注（如果有）
  if (student.remark && student.remark.length > 500) {
    errors.remark = '备注不能超过500个字符';
  }
  
  // 检查学号是否重复
  const duplicateStudentNo = previewData.value.find(
    s => s.studentNo === student.studentNo && s !== student
  );
  if (duplicateStudentNo) {
    errors.studentNo = '学号重复';
  }
  
  return errors;
}

// 导入数据
const importData = () => {
  if (validCount.value === 0) {
    ElMessage.warning('没有有效数据可导入')
    return
  }
  
  ElMessageBox.confirm(
    `确定要导入${validCount.value}条学生数据吗？`,
    '导入确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  )
  .then(async () => {
    importing.value = true
    activeStep.value = 3
    
    try {
      // 直接上传原始Excel文件
      const formData = new FormData();
      const originalFile = fileList.value[0].raw;
      
      // 添加原始文件到FormData
      formData.append('file', originalFile);
      
      // 发送请求
      const res = await request({
        url: '/edu-student/batchImport',
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 60000 // 增加超时时间到60秒
      })
      
      if (res.data.code === 200) {
        const data = res.data.data
        
        importResult.show = true
        importResult.success = true
        importResult.title = '导入完成'
        importResult.message = `成功导入${data.successCount}条数据，失败${data.failCount}条`
        importResult.totalCount = data.totalCount
        importResult.successCount = data.successCount
        importResult.failCount = data.failCount
        importResult.time = data.time
        importResult.errors = data.errors || []
      } else {
        importResult.show = true
        importResult.success = false
        importResult.title = '导入失败'
        importResult.message = res.data.message || '服务器错误'
      }
    } catch (error) {
      ElMessage.error('导入失败: ' + error.message);
      importResult.show = true
      importResult.success = false
      importResult.title = '导入失败'
      importResult.message = error.message || '网络错误'
    } finally {
      importing.value = false
    }
  })
  .catch(() => {})
}

// 重置导入
const resetImport = () => {
  fileList.value = []
  previewData.value = []
  activeStep.value = 0
  importResult.show = false
}

// 跳转到学生列表
const goToStudentList = () => {
  router.push('/dashboard/student/list')
}
</script>

<style scoped>
.batch-import-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.batch-import-container::-webkit-scrollbar {
  display: none;
}

.upload-card, .preview-card, .result-card, .help-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.upload-card:hover, .preview-card:hover, .result-card:hover, .help-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.upload-component {
  width: 100%;
  max-width: 600px;
}

.upload-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.import-steps {
  margin-top: 30px;
  padding: 0 20px;
}

.table-actions {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.error-cell {
  color: #F56C6C;
  display: flex;
  align-items: center;
  gap: 5px;
}

.error-icon {
  color: #F56C6C;
  font-size: 16px;
}

.help-content {
  padding: 10px;
}

.help-content h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
}

.help-content p {
  margin: 8px 0;
  color: #606266;
}

.mt-20 {
  margin-top: 20px;
}

.import-summary {
  margin-top: 20px;
}

.error-list {
  margin-top: 20px;
}

.error-list h4 {
  margin-bottom: 15px;
  color: #F56C6C;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style> 