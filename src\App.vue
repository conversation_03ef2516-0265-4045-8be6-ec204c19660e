<template>
  <RouterView/>
</template>

<script setup>
import { onMounted } from 'vue';
import Loading from '@/components/common/Loading.vue';
import { useAppStore } from '@/stores/appStore';

const appStore = useAppStore();

onMounted(() => {
  // Initialize app settings if needed
});
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  color: #303133;
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏WebKit浏览器的滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 确保所有可滚动元素都隐藏滚动条 */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 确保el-menu组件在滚动时不会出现问题 */
.el-menu {
  border-right: none !important;
}

.el-menu--collapse .el-sub-menu__title span,
.el-menu--collapse .el-sub-menu__title .el-sub-menu__icon-arrow {
  display: none;
}

/* 全局美化卡片样式 */
.el-card {
  border: none !important;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  overflow: hidden;
}

.el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-2px);
}

.el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

.el-card__body {
  padding: 20px;
}

/* 全局美化按钮样式 */
.el-button {
  border-radius: 4px;
  transition: all 0.2s;
}

.el-button:active {
  transform: scale(0.98);
}

/* 全局美化表格样式 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th.el-table__cell {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #fafafa;
}

/* 表格行悬停效果 */
.el-table__body tr:hover > td.el-table__cell {
  background-color: #ecf5ff !important;
}

/* 全局美化表单样式 */
.el-form-item__label {
  font-weight: 500;
}

.el-input__wrapper,
.el-textarea__inner {
  border-radius: 4px;
  transition: all 0.3s;
}

.el-input__wrapper:hover,
.el-textarea__inner:hover {
  box-shadow: 0 0 0 1px #409EFF inset;
}

/* 全局美化分页样式 */
.el-pagination {
  margin-top: 20px;
  justify-content: flex-end;
}

.el-pagination .el-pagination__total {
  font-weight: 500;
}

/* 全局美化标签页样式 */
.el-tabs__item {
  font-size: 15px;
}

.el-tabs__item.is-active {
  font-weight: 600;
}

/* 全局美化下拉菜单样式 */
.el-dropdown-menu {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.el-dropdown-menu__item {
  padding: 10px 20px;
}

/* 全局美化对话框样式 */
.el-overlay {
  backdrop-filter: blur(2px);
}

.el-dialog {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px;
}

.el-dialog__title {
  font-weight: 600;
  font-size: 18px;
}

/* 修复移动设备上的一些样式问题 */
@media screen and (max-width: 768px) {
  .el-menu-vertical:not(.el-menu--collapse) {
    width: 200px !important;
  }
  
  .el-aside {
    flex-shrink: 0;
  }
  
  .el-dialog {
    width: 90% !important;
    max-width: 90%;
  }
  
  .el-form-item__label {
    padding-bottom: 8px;
  }
  
  .el-message-box {
    width: 90% !important;
    max-width: 90%;
  }
}

/* 添加滚动平滑效果 */
* {
  scroll-behavior: smooth;
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局通用类 */
.text-center {
  text-align: center;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 卡片内容区域无数据时的样式 */
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
}

.empty-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}
</style>