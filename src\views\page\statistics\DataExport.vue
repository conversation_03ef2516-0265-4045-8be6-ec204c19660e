<script setup>
import { ref, reactive } from 'vue'
import { Download, Document, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 筛选表单
const filterForm = reactive({
  exportType: 'student',
  className: '',
  dateRange: [],
  dataType: ['basic', 'points', 'history']
})

// 班级选项
const classOptions = [
  '计算机科学1班',
  '计算机科学2班',
  '软件工程1班',
  '软件工程2班',
  '信息安全1班',
  '数据科学1班',
  '人工智能1班'
]

// 导出类型选项
const exportTypeOptions = [
  { value: 'student', label: '学生数据' },
  { value: 'class', label: '班级数据' },
  { value: 'points', label: '积分记录' },
  { value: 'approval', label: '审批记录' }
]

// 数据类型选项
const dataTypeOptions = [
  { value: 'basic', label: '基本信息' },
  { value: 'points', label: '积分数据' },
  { value: 'history', label: '历史记录' },
  { value: 'statistics', label: '统计数据' }
]

// 导出格式选项
const exportFormatOptions = [
  { value: 'excel', label: 'Excel (.xlsx)' },
  { value: 'csv', label: 'CSV (.csv)' },
  { value: 'pdf', label: 'PDF (.pdf)' }
]

// 导出记录
const exportRecords = ref([
  {
    id: 1,
    fileName: '学生积分数据_20230501.xlsx',
    exportType: '学生数据',
    exportTime: '2023-05-01 15:30:45',
    fileSize: '1.2MB',
    status: 'success'
  },
  {
    id: 2,
    fileName: '班级积分统计_20230510.xlsx',
    exportType: '班级数据',
    exportTime: '2023-05-10 09:15:22',
    fileSize: '0.8MB',
    status: 'success'
  },
  {
    id: 3,
    fileName: '积分历史记录_20230515.csv',
    exportType: '积分记录',
    exportTime: '2023-05-15 14:20:18',
    fileSize: '2.5MB',
    status: 'success'
  },
  {
    id: 4,
    fileName: '审批记录_20230520.pdf',
    exportType: '审批记录',
    exportTime: '2023-05-20 16:45:30',
    fileSize: '1.5MB',
    status: 'success'
  },
  {
    id: 5,
    fileName: '学生积分趋势_20230525.xlsx',
    exportType: '学生数据',
    exportTime: '2023-05-25 10:30:12',
    fileSize: '1.8MB',
    status: 'success'
  }
])

// 导出设置
const exportFormat = ref('excel')
const exportFileName = ref('')
const isExporting = ref(false)

// 方法定义
const handleExport = () => {
  if (!filterForm.dataType || filterForm.dataType.length === 0) {
    ElMessage.warning('请至少选择一种数据类型')
    return
  }
  
  isExporting.value = true
  
  // 生成文件名
  const date = new Date()
  const dateStr = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`
  const typeLabel = exportTypeOptions.find(item => item.value === filterForm.exportType)?.label || '数据'
  exportFileName.value = `${typeLabel}_${dateStr}`
  
  // 模拟导出过程
  setTimeout(() => {
    // 添加新的导出记录
    const newRecord = {
      id: exportRecords.value.length + 1,
      fileName: `${exportFileName.value}.${exportFormat.value}`,
      exportType: typeLabel,
      exportTime: new Date().toLocaleString(),
      fileSize: `${(Math.random() * 2 + 0.5).toFixed(1)}MB`,
      status: 'success'
    }
    
    exportRecords.value.unshift(newRecord)
    isExporting.value = false
    
    ElMessage.success('数据导出成功')
  }, 2000)
}

const downloadFile = (record) => {
  ElMessage.success(`开始下载文件: ${record.fileName}`)
}

const deleteRecord = (record, index) => {
  ElMessageBox.confirm(
    `确定要删除导出记录 "${record.fileName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    exportRecords.value.splice(index, 1)
    ElMessage.success('删除成功')
  })
  .catch(() => {})
}

const getStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'pending': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'success': return '成功'
    case 'pending': return '处理中'
    case 'failed': return '失败'
    default: return '未知'
  }
}
</script>

<template>
  <div class="data-export-container">
    <div class="page-header">
      <h2>数据导出</h2>
    </div>

    <!-- 导出设置区域 -->
    <el-card shadow="hover" class="export-settings-card">
      <template #header>
        <div class="card-header">
          <span>导出设置</span>
        </div>
      </template>
      
      <el-form :model="filterForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="导出类型">
              <el-select v-model="filterForm.exportType" placeholder="请选择导出类型" style="width: 100%">
                <el-option
                  v-for="item in exportTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="班级">
              <el-select v-model="filterForm.className" placeholder="请选择班级" clearable style="width: 100%">
                <el-option
                  v-for="item in classOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="导出格式">
              <el-radio-group v-model="exportFormat">
                <el-radio 
                  v-for="item in exportFormatOptions" 
                  :key="item.value" 
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="数据类型">
          <el-checkbox-group v-model="filterForm.dataType">
            <el-checkbox 
              v-for="item in dataTypeOptions" 
              :key="item.value" 
              :label="item.value"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="文件名">
          <el-input v-model="exportFileName" placeholder="留空则使用默认文件名">
            <template #append>.{{ exportFormat }}</template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleExport" :loading="isExporting">
            <el-icon><Download /></el-icon> 导出数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 导出记录区域 -->
    <el-card shadow="hover" class="export-records-card">
      <template #header>
        <div class="card-header">
          <span>导出记录</span>
        </div>
      </template>
      
      <el-table :data="exportRecords" style="width: 100%">
        <el-table-column prop="fileName" label="文件名" min-width="220">
          <template #default="scope">
            <div class="file-name">
              <el-icon><Document /></el-icon>
              <span>{{ scope.row.fileName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="exportType" label="数据类型" width="120" />
        <el-table-column prop="exportTime" label="导出时间" width="180" />
        <el-table-column prop="fileSize" label="文件大小" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="downloadFile(scope.row)">
              <el-icon><Download /></el-icon> 下载
            </el-button>
            <el-button type="danger" link @click="deleteRecord(scope.row, scope.$index)">
              <el-icon><Delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 导出说明区域 -->
    <el-card shadow="hover" class="export-guide-card">
      <template #header>
        <div class="card-header">
          <span>导出说明</span>
        </div>
      </template>
      
      <div class="guide-content">
        <h3>数据导出说明</h3>
        <p>1. 导出的数据将根据您选择的筛选条件进行过滤，请确保选择正确的条件。</p>
        <p>2. 导出格式支持Excel、CSV和PDF，不同格式可能会影响数据的展示方式。</p>
        <p>3. 数据类型可多选，选择的类型越多，导出文件可能越大。</p>
        <p>4. 导出记录将保留最近30天的导出历史，超过时间的记录将自动清除。</p>
        <p>5. 如需批量导出大量数据，建议在网络稳定的环境下操作。</p>
        
        <h3>数据安全提示</h3>
        <p>1. 导出的数据可能包含敏感信息，请妥善保管。</p>
        <p>2. 使用完毕后请及时删除本地文件，避免信息泄露。</p>
        <p>3. 所有导出操作将被记录，请遵守学校相关数据使用规定。</p>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.data-export-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.data-export-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.export-settings-card,
.export-records-card,
.export-guide-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.export-settings-card:hover,
.export-records-card:hover,
.export-guide-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.file-name {
  display: flex;
  align-items: center;
}

.file-name .el-icon {
  margin-right: 5px;
  color: #909399;
}

.guide-content {
  line-height: 1.6;
}

.guide-content h3 {
  font-size: 16px;
  margin-top: 15px;
  margin-bottom: 10px;
  color: #303133;
}

.guide-content p {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>