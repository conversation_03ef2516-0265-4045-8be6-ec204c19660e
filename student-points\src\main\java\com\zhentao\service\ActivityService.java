package com.zhentao.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zhentao.dto.system.system.ActivityDto;
import com.zhentao.pojo.Activity;
import com.zhentao.utils.Result;

/**
 * <p>
 * 活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public interface ActivityService extends IService<Activity> {
    /**
     * 添加活动
     */
    public Result addActivity(ActivityDto activityDto);
    /**
     * 删除活动
     */
    public Result deleteActivity(Integer activityId);
    /**
     * 修改活动
     */
    public Result updateActivity(ActivityDto activityDto);
    /**
     * 查询活动
     */
    public Result findActivity();
    /**
     * 改变活动状态
     */
    public Result changeActivityStatus(Integer activityId, Integer status);

}
