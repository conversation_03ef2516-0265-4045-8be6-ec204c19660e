<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { updateUserPassword } from '@/api/system/user.js'

const formRef = ref(null)
const loading = ref(false)

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value === passwordForm.value.oldPassword) {
          callback(new Error('新密码不能与旧密码相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 提交表单修改密码
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {

        updateUserPassword(passwordForm.value).then(res => {
          if (res.data.code === 200) {
            ElMessage.success('密码修改成功，请重新登录')
            passwordForm.value.newPassword=""
            passwordForm.value.confirmPassword=""
            // 密码修改成功后清除登录信息，跳转到登录页
            setTimeout(() => {
              localStorage.clear()
              window.location.href = '/login'
            }, 1500)
          } else {
            ElMessage.error(res.data.message || '密码修改失败')
          }
        })
      } catch (error) {
        console.error('修改密码出错:', error)
        ElMessage.error('修改密码失败，请重试')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<template>
  <div class="change-password-container">
    <div class="page-header">
      <h2>修改密码</h2>
    </div>
    
    <el-card class="password-card">
      <el-form
        ref="formRef"
        :model="passwordForm"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
              v-model="passwordForm.oldPassword"
              type="password"
              placeholder="请输入原密码"
              show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码" 
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码" 
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">确认修改</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
        
        <div class="password-tips">
          <p>密码安全提示：</p>
          <ul>
            <li>密码长度不少于6个字符</li>
            <li>建议使用字母、数字和特殊字符的组合</li>
            <li>定期更换密码可以提高账号安全性</li>
          </ul>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.change-password-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.password-card {
  max-width: 600px;
  margin: 0 auto;
}

.password-tips {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}

.password-tips p {
  margin: 0 0 10px 0;
  font-weight: bold;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.password-tips li {
  margin-bottom: 5px;
}
</style>