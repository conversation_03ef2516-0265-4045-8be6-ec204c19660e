package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Getter
@Setter
@TableName("activity_application")
public class ActivityApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报名主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报名人姓名
     */
    @TableField("`name`")
    private String name;
    /**
     * 报名人手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 报名人所在班级
     */
    private String className;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 选择活动
     */
    private Integer activityId;
    @TableField(exist = false)
    private String activityName;
}
