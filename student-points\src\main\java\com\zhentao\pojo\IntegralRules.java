package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Getter
@Setter
@TableName("integral_rules")
public class IntegralRules implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则序号（主键）
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 分类
     */
    private String category;

    /**
     * 积分值
     */
    private Integer score;

    /**
     * 规则描述
     */
    @TableField("`description`")
    private String description;

    /**
     * 规则状态(1：启用 2：禁用)
     */
    @TableField("`status`")
    private Integer status;
    @TableField(exist = false)
    private Integer pageSize;
    @TableField(exist = false)
    private Integer pageNum;
    @TableField(exist = false)
    private String mhc;
}
