package com.zhentao.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsApply;
import com.zhentao.pojo.PointsRecord;
import com.zhentao.service.*;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 积分变动申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/points-apply")
public class PointsApplyController {
    @Autowired
    PointsApplyService pointsApplyService;
    @Autowired
    EduStudentController eduStudentController;
    @Autowired
    EduStudentService eduStudentService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    EduClassService eduClassService;
    @Autowired
    PointsRecordService pointsRecordService;
    @RequestMapping("/list0")
    public Result list0(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",1);
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        List<PointsApply> list=pointsApplyService.list(queryWrapper);
        for (PointsApply pointsApply:list){
            pointsApply.setStudent(eduStudentService.getById(pointsApply.getStudentId()));
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/list1")
    public Result list1(@RequestBody PointsApply pointsApply1){
//        QueryWrapper<EduStudent> queryWrapper=new QueryWrapper<>();
//        queryWrapper.like("real_name",eduStudent.getRealName());
//        List<EduStudent> list=eduStudentService.list(queryWrapper);
//        for (EduStudent student:list){
//            QueryWrapper<PointsApply> queryWrapper1=new QueryWrapper<>();
//            queryWrapper1.eq("student_id",student.getStudentId());
//            PointsApply pointsApply=pointsApplyService.getOne(queryWrapper1);
//            student.setPointsApply(pointsApply);
//        }
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("status",2)
                .or()
                .eq("status",3);
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        List<PointsApply> list=pointsApplyService.list(queryWrapper);
        for (PointsApply pointsApply:list){
            pointsApply.setStudent(eduStudentService.getById(pointsApply.getStudentId()));
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }

        return list!=null?Result.OK(list):Result.ERROR();

    }
    @RequestMapping("/list2")
    public Result list2(@RequestBody PointsApply pointsApply1){
        QueryWrapper<PointsApply> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq(pointsApply1.getApplyUserId()!=null && pointsApply1.getApplyUserId()!=0,"apply_user_id",pointsApply1.getApplyUserId());
        queryWrapper.eq(pointsApply1.getPointsChange()!=null && pointsApply1.getPointsChange()!=0,"points_change",pointsApply1.getPointsChange());
        queryWrapper.eq(pointsApply1.getClassId()!=null && pointsApply1.getClassId()!=0,"class_id",pointsApply1.getClassId());
        queryWrapper.eq(pointsApply1.getStatus()!=null && pointsApply1.getStatus()!=0,"status",pointsApply1.getStatus());
        List<PointsApply> list=pointsApplyService.list(queryWrapper);
        for (PointsApply pointsApply:list){
            pointsApply.setStudent(eduStudentService.getById(pointsApply.getStudentId()));
            pointsApply.setApplyUser(sysUserService.getById(pointsApply.getApplyUserId()));
            pointsApply.setReviewer(sysUserService.getById(pointsApply.getReviewerId()));
            pointsApply.setCreateUser(sysUserService.getById(pointsApply.getCreateBy()));
            pointsApply.setUpdateUser(sysUserService.getById(pointsApply.getUpdateBy()));
            pointsApply.setEduClass(eduClassService.getById(pointsApply.getClassId()));
        }
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/class")
    public Result findClass(){
        List<EduClass> list=eduClassService.list();
        return list!=null?Result.OK(list):Result.ERROR();
    }
    @RequestMapping("/tg")
    public Result tg(Integer id){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus(2);

        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());

        EduStudent eduStudent=eduStudentService.getById(pointsApply.getStudentId());
        if (pointsApply.getPointsChange()==1){
            EduStudent byId = eduStudentService.getById(pointsApply.getStudentId());
            if(byId.getDelFlag().equals("1")){
                return Result.ERROR("该学生已删除，加分失败");
            }
            if (byId.getStatus().equals("1")){
                return Result.ERROR("该学生已休学，加分失败");
            }
            if (byId.getStatus().equals("2")){
                return Result.ERROR("该学生已退学，加分失败");
            }
            if (byId.getStatus().equals("3")){
                return Result.ERROR("该学生已毕业，加分失败");
            }
            Integer points=byId.getPoints()+pointsApply.getPoints();
            PointsRecord pointsRecord=new PointsRecord();
            pointsRecord.setStudentId(pointsApply.getStudentId());
            pointsRecord.setPointsBefore(byId.getPoints());
            pointsRecord.setPointsChange(pointsApply.getPointsChange());
            pointsRecord.setPointsAfter(points);
            pointsRecord.setApplyId(pointsApply.getApplyId());
            pointsRecord.setOperationType(3);
            pointsRecord.setOperationTime(new Date());
            pointsRecord.setReason(pointsApply.getReason());
            pointsRecord.setCreateBy(UserContext.getCurrentUser().getUserId());
            pointsRecord.setCreateTime(new Date());
            pointsRecord.setDelFlag(1);
            pointsRecordService.save(pointsRecord);

            byId.setPoints(points);
            byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
            byId.setUpdateTime(new Date());
            boolean b = eduStudentService.updateById(byId);
        }else{
            EduStudent byId = eduStudentService.getById(pointsApply.getStudentId());
            if(byId.getDelFlag().equals("1")){
                return Result.ERROR("该学生已删除，减分失败");
            }
            if (byId.getStatus().equals("1")){
                return Result.ERROR("该学生已休学，减分失败");
            }
            if (byId.getStatus().equals("2")){
                return Result.ERROR("该学生已退学，减分失败");
            }
            if (byId.getStatus().equals("3")){
                return Result.ERROR("该学生已毕业，减分失败");
            }


            byId.setPoints(byId.getPoints()-pointsApply.getPoints());
            byId.setUpdateBy(UserContext.getCurrentUser().getUserId());
            byId.setUpdateTime(new Date());
            boolean b = eduStudentService.updateById(byId);
        }

        boolean b = pointsApplyService.updateById(pointsApply);
        return b?Result.OK():Result.ERROR();
    }
    @RequestMapping("/jj")
    public Result jj(Integer id){
        PointsApply pointsApply=pointsApplyService.getById(id);
        pointsApply.setStatus(3);
        pointsApply.setReviewerId(UserContext.getCurrentUser().getUserId());
        pointsApply.setReviewTime(new Date());
        boolean b = pointsApplyService.updateById(pointsApply);

        EduStudent byId = eduStudentService.getById(pointsApply.getStudentId());
        Integer points=byId.getPoints()+pointsApply.getPoints();
        PointsRecord pointsRecord=new PointsRecord();
        pointsRecord.setStudentId(pointsApply.getStudentId());
        pointsRecord.setPointsBefore(byId.getPoints());
        pointsRecord.setPointsChange(pointsApply.getPointsChange());
        pointsRecord.setPointsAfter(points);
        pointsRecord.setApplyId(pointsApply.getApplyId());
        pointsRecord.setOperationType(4);
        pointsRecord.setOperationTime(new Date());
        pointsRecord.setReason(pointsApply.getReason());
        pointsRecord.setCreateBy(UserContext.getCurrentUser().getUserId());
        pointsRecord.setCreateTime(new Date());
        pointsRecord.setDelFlag(1);
        pointsRecordService.save(pointsRecord);

        return b?Result.OK():Result.ERROR();
    }
    // -------------------------------------------------------

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @RequestMapping("addJiFen")
    public Result addJiFen(@RequestBody PointsApply pointsApply){
        // 添加调试日志
        System.out.println("接收到的积分申请数据: " + pointsApply);
        System.out.println("积分变动值: " + pointsApply.getPointsChange());
        return pointsApplyService.addJiFen(pointsApply);
    }

    @RequestMapping("list")
    public Result list(@RequestBody(required = false) Map<String, Object> params) {
        return pointsApplyService.listPointsApplies(params);
    }

    /**
     * 获取积分历史记录，支持多种过滤条件
     * @param pointsApply 过滤参数，可包含：
     *               - studentId: 学生ID
     *               - className: 班级名称
     *               - pointsChange: 积分类型（1-加分，2-减分）
     *               - category: 积分项目类别
     *               - status: 状态（1-待审核，2-已通过，3-已拒绝）
     *               - startDate: 开始日期
     *               - endDate: 结束日期
     * @return 积分历史记录列表
     */
    @RequestMapping("history")
    public Result getPointsHistory(@RequestBody(required = false) Object param) {
        try {
            System.out.println("接收到的参数类型: " + (param != null ? param.getClass().getName() : "null"));
            System.out.println("接收到的参数内容: " + param);

            // 处理不同类型的参数
            if (param instanceof PointsApply) {
                // 如果是PointsApply对象，直接传递
                PointsApply pointsApply = (PointsApply) param;
                // 设置默认分页参数
                if (pointsApply.getPageNum() == null) {
                    pointsApply.setPageNum(1);
                }
                if (pointsApply.getPageSize() == null) {
                    pointsApply.setPageSize(10);
                }
                return pointsApplyService.getPointsHistory(pointsApply);
            } else if (param instanceof Map) {
                // 如果是Map对象，转换为PointsApply对象
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMap = (Map<String, Object>) param;
                PointsApply pointsApply = new PointsApply();

                // 设置分页参数
                pointsApply.setPageNum(paramMap.containsKey("pageNum") ? parseInteger(paramMap.get("pageNum")) : 1);
                pointsApply.setPageSize(paramMap.containsKey("pageSize") ? parseInteger(paramMap.get("pageSize")) : 10);

                // 设置各个字段
                if (paramMap.containsKey("studentId")) {
                    pointsApply.setStudentId(parseInteger(paramMap.get("studentId")));
                }
                if (paramMap.containsKey("classId")) {
                    pointsApply.setClassId(parseInteger(paramMap.get("classId")));
                }
                if (paramMap.containsKey("className")) {
                    // 班级名称需要特殊处理，存储在临时字段中
                    pointsApply.setRemark((String) paramMap.get("className"));
                }
                if (paramMap.containsKey("pointsChange")) {
                    pointsApply.setPointsChange(parseInteger(paramMap.get("pointsChange")));
                }
                if (paramMap.containsKey("status")) {
                    pointsApply.setStatus(parseInteger(paramMap.get("status")));
                }
                if (paramMap.containsKey("category")) {
                    // 类别需要特殊处理，存储在临时字段中
                    pointsApply.setReason((String) paramMap.get("category"));
                }

                // 处理日期范围 - 修复日期转换
                try {
                    if (paramMap.containsKey("startTime")) {
                        Object startTimeObj = paramMap.get("startTime");
                        if (startTimeObj instanceof String) {
                            // 如果是字符串，需要转换为Date
                            pointsApply.setStartTime(parseDate((String) startTimeObj));
                        } else if (startTimeObj instanceof Date) {
                            pointsApply.setStartTime((Date) startTimeObj);
                        }
                    }

                    if (paramMap.containsKey("endTime")) {
                        Object endTimeObj = paramMap.get("endTime");
                        if (endTimeObj instanceof String) {
                            // 如果是字符串，需要转换为Date
                            pointsApply.setEndTime(parseDate((String) endTimeObj));
                        } else if (endTimeObj instanceof Date) {
                            pointsApply.setEndTime((Date) endTimeObj);
                        }
                    }
                } catch (Exception e) {
                    System.out.println("日期转换错误: " + e.getMessage());
                    // 如果日期转换失败，设置为null
                    pointsApply.setStartTime(null);
                    pointsApply.setEndTime(null);
                }

                return pointsApplyService.getPointsHistory(pointsApply);
            } else {
                // 如果是null或其他类型，传递空对象
                PointsApply pointsApply = new PointsApply();
                pointsApply.setPageNum(1);
                pointsApply.setPageSize(10);
                return pointsApplyService.getPointsHistory(pointsApply);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 解析日期字符串为Date对象
     * 支持多种格式的日期字符串
     */
    private Date parseDate(String dateStr) throws Exception {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        // 处理ISO格式的日期字符串 (2025-07-14T16:00:00.000Z)
        if (dateStr.contains("T")) {
            // 移除末尾的Z并替换T为空格
            dateStr = dateStr.replace("T", " ").replace("Z", "");
            // 如果有毫秒，保留3位
            if (dateStr.contains(".")) {
                int dotIndex = dateStr.indexOf(".");
                String beforeDot = dateStr.substring(0, dotIndex);
                String afterDot = dateStr.substring(dotIndex + 1);
                if (afterDot.length() > 3) {
                    afterDot = afterDot.substring(0, 3);
                }
                dateStr = beforeDot + "." + afterDot;
            }
        }

        // 尝试多种日期格式
        String[] dateFormats = {
                "yyyy-MM-dd HH:mm:ss.SSS",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd"
        };

        for (String format : dateFormats) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(format);
                return sdf.parse(dateStr);
            } catch (Exception e) {
                // 尝试下一个格式
                continue;
            }
        }

        // 所有格式都失败，抛出异常
        throw new Exception("无法解析日期: " + dateStr);
    }

    /**
     * 解析整数值，处理不同类型的输入
     */
    private Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (value instanceof Long) {
            return ((Long) value).intValue();
        } else if (value instanceof Double) {
            return ((Double) value).intValue();
        }

        return null;
    }

    /**
     * 获取积分统计信息
     * @param params 过滤参数，可包含：
     *               - studentId: 学生ID
     *               - classId: 班级ID
     *               - startDate: 开始日期
     *               - endDate: 结束日期
     * @return 积分统计信息
     */
    @RequestMapping("statistics")
    public Result getPointsStatistics(@RequestBody(required = false) Map<String, Object> params) {
        return pointsApplyService.getPointsStatistics(params);
    }

    /**
     * 撤销积分申请
     * @param params 包含applyId的参数Map
     * @return 操作结果
     */
    @RequestMapping("cancel")
    public Result cancelPointsApplication(@RequestBody Map<String, Object> params) {
        try {
            if (params == null || !params.containsKey("applyId")) {
                return Result.ERROR("申请ID不能为空");
            }

            Integer applyId = parseInteger(params.get("applyId"));
            if (applyId == null) {
                return Result.ERROR("无效的申请ID");
            }

            return pointsApplyService.cancelPointsApplication(applyId);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("撤销申请失败: " + e.getMessage());
        }
    }

    /**
     * 调试端点：直接查询数据库中的积分申请记录
     * @return 积分申请记录列表
     */
    @RequestMapping("debug")
    public Result debug() {
        try {
            // 直接执行SQL查询
            String sql = "SELECT * FROM points_apply LIMIT 10";
            List<Map<String, Object>> records = jdbcTemplate.queryForList(sql);

            // 输出记录数量和第一条记录
            System.out.println("查询到 " + records.size() + " 条记录");
            if (!records.isEmpty()) {
                System.out.println("第一条记录: " + records.get(0));
            }

            // 查询表结构
            String schemaSql = "SHOW COLUMNS FROM points_apply";
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(schemaSql);
            System.out.println("表结构: " + columns);

            // 查询积分添加记录
            String addSql = "SELECT * FROM points_apply WHERE points_change = 1 LIMIT 10";
            List<Map<String, Object>> addRecords = jdbcTemplate.queryForList(addSql);
            System.out.println("积分添加记录: " + addRecords.size() + " 条");

            // 查询积分扣除记录
            String deductSql = "SELECT * FROM points_apply WHERE points_change = 2 LIMIT 10";
            List<Map<String, Object>> deductRecords = jdbcTemplate.queryForList(deductSql);
            System.out.println("积分扣除记录: " + deductRecords.size() + " 条");

            // 返回所有查询结果
            Map<String, Object> result = new HashMap<>();
            result.put("allRecords", records);
            result.put("addRecords", addRecords);
            result.put("deductRecords", deductRecords);
            result.put("tableStructure", columns);

            return Result.OK(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }
//------------------------------------------------虚线一下都是cmy写的-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    /**
     * 查询所有当天学生的一个加分记录
     */
    @PostMapping("/queryTodayAddPoints")
    public Result queryTodayAddPoints(){
        return pointsApplyService.queryTodayAddPoints();
    }

    /**
     * 查询所有当天学生一个减分记录
     */
    @PostMapping("/queryTodayMinusPoints")
    public Result queryTodayMinusPoints(){
        return pointsApplyService.queryTodayMinusPoints();
    }
}
