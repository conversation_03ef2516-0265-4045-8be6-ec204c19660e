package com.zhentao.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhentao.dto.system.system.ApplicationDto;
import com.zhentao.dto.system.system.PageActivityApplicationDto;
import com.zhentao.pojo.ActivityApplication;
import com.zhentao.service.ActivityApplicationService;
import com.zhentao.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 活动表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@RestController
@RequestMapping("/activityApplication")
public class ActivityApplicationController {
    @Autowired
    private ActivityApplicationService activityApplicationService;
    /**
     * 报名活动
     */
    @PostMapping("/signUpActivity")
    public Result signUpActivity(@RequestBody ApplicationDto applicationDto) {
        return activityApplicationService.signUpActivity(applicationDto);
    }
    /**
     * 取消报名活动
     */
    @PostMapping("/cancelSignUpActivity")
    public Result cancelSignUpActivity(@RequestParam Integer applicationId, @RequestParam Integer activityId) {
        return activityApplicationService.cancelSignUpActivity(applicationId, activityId);
    }
    /**
     * 活动报名列表
     */
    @PostMapping("/activityApplicationPage")
    public Page<ActivityApplication> activityApplicationPage(@RequestBody PageActivityApplicationDto pageActivityApplicationDto) {
        System.err.println(pageActivityApplicationDto.toString());
        return activityApplicationService.activityApplicationPage(pageActivityApplicationDto);
    }
    /**
     * 查询所有活动
     */
    @PostMapping("/findActivity")
    public Result findActivity() {
        return activityApplicationService.findActivity();
    }

}
