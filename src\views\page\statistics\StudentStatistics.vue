<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 筛选表单
const filterForm = reactive({
  studentId: '',
  studentName: '',
  className: '',
  dateRange: []
})

// 班级选项
const classOptions = [
  '计算机科学1班',
  '计算机科学2班',
  '软件工程1班',
  '软件工程2班',
  '信息安全1班',
  '数据科学1班',
  '人工智能1班'
]

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 图表实例
const pointsTrendChart = ref(null)
const pointsDistributionChart = ref(null)
const pointsTypeChart = ref(null)

// 图表对象
let trendChartInstance = null
let distributionChartInstance = null
let typeChartInstance = null

// 学生积分排名数据
const rankingData = ref([])

// 生命周期钩子
onMounted(() => {
  loadData()
  // 使用nextTick确保DOM元素已经渲染
  setTimeout(() => {
    initCharts()
  }, 300)
})

// 组件卸载时清理图表实例和事件监听器
onUnmounted(() => {
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  if (distributionChartInstance) {
    distributionChartInstance.dispose()
  }
  if (typeChartInstance) {
    typeChartInstance.dispose()
  }
  // 移除窗口resize事件监听
  window.removeEventListener('resize', handleResize)
})

// 统一的resize处理函数
const handleResize = () => {
  if (trendChartInstance) {
    trendChartInstance.resize()
  }
  if (distributionChartInstance) {
    distributionChartInstance.resize()
  }
  if (typeChartInstance) {
    typeChartInstance.resize()
  }
}

// 添加resize事件监听
window.addEventListener('resize', handleResize)

// 方法定义
const handleSearch = () => {
  currentPage.value = 1
  loadData()
  refreshCharts()
}

const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'dateRange') {
      filterForm[key] = []
    } else {
      filterForm[key] = ''
    }
  })
  currentPage.value = 1
  loadData()
  refreshCharts()
}

const loadData = () => {
  loading.value = true
  // 模拟加载数据
  setTimeout(() => {
    tableData.value = generateMockData()
    total.value = 100
    rankingData.value = generateRankingData()
    loading.value = false
  }, 500)
}

const refreshData = () => {
  loadData()
  refreshCharts()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

const initCharts = () => {
  // 初始化积分趋势图
  if (pointsTrendChart.value) {
    // 如果已存在实例，先销毁
    if (trendChartInstance) {
      trendChartInstance.dispose()
    }
    
    trendChartInstance = echarts.init(pointsTrendChart.value)
    const option = {
      title: {
        text: '学生积分趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value',
        name: '积分'
      },
      series: [
        {
          name: '总积分',
          type: 'line',
          data: [10, 15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80],
          smooth: true,
          lineStyle: {
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '增加积分',
          type: 'line',
          data: [10, 12, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60],
          smooth: true,
          lineStyle: {
            color: '#67C23A'
          },
          itemStyle: {
            color: '#67C23A'
          }
        },
        {
          name: '扣除积分',
          type: 'line',
          data: [0, -5, -10, -15, -20, -25, -30, -35, -40, -45, -50, -55],
          smooth: true,
          lineStyle: {
            color: '#F56C6C'
          },
          itemStyle: {
            color: '#F56C6C'
          }
        }
      ]
    }
    trendChartInstance.setOption(option)
  }

  // 初始化积分分布图
  if (pointsDistributionChart.value) {
    // 如果已存在实例，先销毁
    if (distributionChartInstance) {
      distributionChartInstance.dispose()
    }
    
    distributionChartInstance = echarts.init(pointsDistributionChart.value)
    const option = {
      title: {
        text: '积分分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '积分类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 40, name: '学习成绩' },
            { value: 20, name: '课外活动' },
            { value: 15, name: '志愿服务' },
            { value: 10, name: '班级贡献' },
            { value: 15, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    distributionChartInstance.setOption(option)
  }

  // 初始化积分类型图
  if (pointsTypeChart.value) {
    // 如果已存在实例，先销毁
    if (typeChartInstance) {
      typeChartInstance.dispose()
    }
    
    typeChartInstance = echarts.init(pointsTypeChart.value)
    const option = {
      title: {
        text: '积分类型分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['增加积分', '扣除积分'],
        top: 'bottom'
      },
      xAxis: {
        type: 'category',
        data: ['学习成绩', '课外活动', '志愿服务', '班级贡献', '其他']
      },
      yAxis: {
        type: 'value',
        name: '积分'
      },
      series: [
        {
          name: '增加积分',
          type: 'bar',
          stack: 'total',
          label: {
            show: true
          },
          emphasis: {
            focus: 'series'
          },
          data: [40, 20, 15, 10, 15],
          itemStyle: {
            color: '#67C23A'
          }
        },
        {
          name: '扣除积分',
          type: 'bar',
          stack: 'total',
          label: {
            show: true
          },
          emphasis: {
            focus: 'series'
          },
          data: [-10, -5, -3, -2, -5],
          itemStyle: {
            color: '#F56C6C'
          }
        }
      ]
    }
    typeChartInstance.setOption(option)
  }
}

const refreshCharts = () => {
  // 在实际应用中，这里应该根据筛选条件重新加载图表数据
  setTimeout(() => {
    initCharts()
  }, 300)
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  
  for (let i = 1; i <= 10; i++) {
    const studentId = `2020${String(i).padStart(3, '0')}`
    const totalPoints = Math.floor(Math.random() * 100) + 50
    const addPoints = Math.floor(Math.random() * 80) + 40
    const deductPoints = Math.floor(Math.random() * 30)
    
    data.push({
      id: i,
      studentId,
      studentName: `学生${i}`,
      className: classOptions[Math.floor(Math.random() * classOptions.length)],
      totalPoints,
      addPoints,
      deductPoints,
      rank: Math.floor(Math.random() * 100) + 1,
      lastMonthRank: Math.floor(Math.random() * 100) + 1
    })
  }
  
  return data
}

// 生成排名数据
const generateRankingData = () => {
  const data = []
  
  for (let i = 1; i <= 10; i++) {
    data.push({
      rank: i,
      studentId: `2020${String(i).padStart(3, '0')}`,
      studentName: `学生${i}`,
      className: classOptions[Math.floor(Math.random() * classOptions.length)],
      totalPoints: 100 - (i * 5) + Math.floor(Math.random() * 10),
      change: i <= 3 ? 'up' : (i >= 8 ? 'down' : 'unchanged')
    })
  }
  
  return data
}
</script>

<template>
  <div class="student-statistics-container">
    <div class="page-header">
      <h2>学生积分分析</h2>
      <el-button type="primary" :icon="Refresh" circle @click="refreshData" />
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :model="filterForm" inline>
        <el-form-item label="学号">
          <el-input v-model="filterForm.studentId" placeholder="请输入学号" clearable />
        </el-form-item>
        
        <el-form-item label="姓名">
          <el-input v-model="filterForm.studentName" placeholder="请输入姓名" clearable />
        </el-form-item>
        
        <el-form-item label="班级">
          <el-select v-model="filterForm.className" placeholder="请选择班级" clearable>
            <el-option
              v-for="item in classOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 概览卡片 -->
    <div class="overview-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">总学生数</div>
            <div class="overview-value">1024</div>
            <div class="overview-footer">全校学生总数</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">平均积分</div>
            <div class="overview-value">78.5</div>
            <div class="overview-footer">较上月 <span class="up">↑2.3</span></div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">最高积分</div>
            <div class="overview-value">98</div>
            <div class="overview-footer">学生: 张三 (2020001)</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="overview-title">最低积分</div>
            <div class="overview-value">45</div>
            <div class="overview-footer">学生: 李四 (2020042)</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <div ref="pointsTrendChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div ref="pointsDistributionChart" class="chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div ref="pointsTypeChart" class="chart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 排名区域 -->
    <div class="ranking-container">
      <el-card shadow="hover" class="ranking-card">
        <template #header>
          <div class="card-header">
            <span>学生积分排名 (Top 10)</span>
          </div>
        </template>
        <el-table :data="rankingData" style="width: 100%" size="large">
          <el-table-column prop="rank" label="排名" width="80" align="center">
            <template #default="scope">
              <div class="rank-badge" :class="{ 'top-three': scope.row.rank <= 3 }">
                {{ scope.row.rank }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="studentId" label="学号" width="120" />
          <el-table-column prop="studentName" label="姓名" width="120" />
          <el-table-column prop="className" label="班级" min-width="180" />
          <el-table-column prop="totalPoints" label="总积分" width="100" sortable />
          <el-table-column label="变化" width="100" align="center">
            <template #default="scope">
              <el-tag 
                :type="scope.row.change === 'up' ? 'success' : (scope.row.change === 'down' ? 'danger' : 'info')"
                effect="plain"
              >
                {{ scope.row.change === 'up' ? '↑' : (scope.row.change === 'down' ? '↓' : '-') }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>学生积分详情</span>
          </div>
        </template>
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column prop="studentId" label="学号" width="120" />
          <el-table-column prop="studentName" label="姓名" width="120" />
          <el-table-column prop="className" label="班级" min-width="180" />
          <el-table-column prop="totalPoints" label="总积分" width="100" sortable>
            <template #default="scope">
              <span class="total-points">{{ scope.row.totalPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="addPoints" label="增加积分" width="100">
            <template #default="scope">
              <span class="positive">+{{ scope.row.addPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="deductPoints" label="扣除积分" width="100">
            <template #default="scope">
              <span class="negative">-{{ scope.row.deductPoints }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="rank" label="当前排名" width="100" sortable />
          <el-table-column label="排名变化" width="120">
            <template #default="scope">
              <span :class="scope.row.lastMonthRank > scope.row.rank ? 'up' : (scope.row.lastMonthRank < scope.row.rank ? 'down' : '')">
                {{ scope.row.lastMonthRank > scope.row.rank ? '↑' : (scope.row.lastMonthRank < scope.row.rank ? '↓' : '-') }}
                {{ Math.abs(scope.row.lastMonthRank - scope.row.rank) || 0 }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:currentPage="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.student-statistics-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.student-statistics-container::-webkit-scrollbar {
  display: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: visible;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.filter-container:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.overview-container {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.overview-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.overview-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.overview-footer {
  font-size: 12px;
  color: #909399;
}

.charts-container {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.chart-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.chart {
  height: 400px;
  width: 100%;
}

.chart-row {
  margin-top: 20px;
}

.ranking-container {
  margin-bottom: 20px;
}

.ranking-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.ranking-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.rank-badge {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background-color: #909399;
  color: #fff;
}

.rank-badge.top-three {
  background-color: #E6A23C;
  font-weight: bold;
}

.table-container {
  margin-bottom: 20px;
}

.table-container .el-card {
  border-radius: 8px;
  overflow: visible;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.table-container .el-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.positive {
  color: #67c23a;
  font-weight: bold;
}

.negative {
  color: #f56c6c;
  font-weight: bold;
}

.total-points {
  font-weight: bold;
  color: #409EFF;
}

.up {
  color: #67c23a;
}

.down {
  color: #f56c6c;
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>