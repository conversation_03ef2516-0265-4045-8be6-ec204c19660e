<template>
  <div class="points-rules-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="form-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>{{ isEdit ? '编辑积分规则' : '添加积分规则' }}</h3>
            </div>
          </template>

          <el-form :model="ruleForm" :rules="rules" ref="ruleFormRef" label-width="100px">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称"></el-input>
            </el-form-item>

            <el-form-item label="规则类型" prop="type">
              <el-radio-group v-model="ruleForm.type">
                <el-radio label="add">加分规则</el-radio>
                <el-radio label="deduct">扣分规则</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="积分分类" prop="category">
              <el-select v-model="ruleForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="积分数值" prop="score">
              <el-input-number v-model="ruleForm.score" :min="1" :max="100" style="width: 180px"></el-input-number>
            </el-form-item>

            <el-form-item label="规则描述" prop="description">
              <el-input v-model="ruleForm.description" type="textarea" :rows="4" placeholder="请输入规则详细描述"></el-input>
            </el-form-item>

            <el-form-item label="是否启用" prop="status">
              <el-switch v-model="ruleForm.status"></el-switch>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitForm(ruleFormRef)">{{ isEdit ? '保存' : '添加' }}</el-button>
              <el-button @click="resetForm(ruleFormRef)">重置</el-button>
              <el-button v-if="isEdit" @click="cancelEdit">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :span="16">
        <el-card class="table-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>积分规则列表</h3>
              <div class="header-actions">
                <el-input
                    v-model="page.mhc"
                    placeholder="搜索规则名称/描述"
                    style="width: 220px"
                    clearable
                    @keyup.enter="searchRules"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click="refreshTable" :icon="Refresh">刷新</el-button>
              </div>
            </div>
          </template>

          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="加分规则" name="add">
              <el-table :data="addRulesData" style="width: 100%" v-loading="loading" border stripe>
                <el-table-column type="index" width="60" label="序号" />
                <el-table-column prop="ruleName" label="规则名称" min-width="150" show-overflow-tooltip />
                <el-table-column prop="category" label="分类" width="120" />
                <el-table-column prop="score" label="积分" width="80">
                  <template #default="scope">
                    <el-tag type="success">+{{ scope.row.score }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="scope">
                    <el-tag :type="scope.row.status ? 'success' : 'info'">
                      {{ scope.row.status ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                  <template #default="scope">
                    <el-button link type="primary" size="small" @click="editRule(scope.row)">编辑</el-button>
                    <el-button link type="danger" size="small" @click="deleteRule(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane label="扣分规则" name="deduct">
              <el-table :data="deductRulesData" style="width: 100%" v-loading="loading" border stripe>
                <el-table-column type="index" width="60" label="序号" />
                <el-table-column prop="ruleName" label="规则名称" min-width="150" show-overflow-tooltip />
                <el-table-column prop="category" label="分类" width="120" />
                <el-table-column prop="score" label="积分" width="80">
                  <template #default="scope">
                    <el-tag type="danger">-{{ scope.row.score }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
                <el-table-column prop="enabled" label="状态" width="80">
                  <template #default="scope">
                    <el-tag :type="scope.row.status ? 'success' : 'info'">
                      {{ scope.row.enabled ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                  <template #default="scope">
                    <el-button link type="primary" size="small" @click="editRule(scope.row)">编辑</el-button>
                    <el-button link type="primary" size="small" @click="toggleRuleStatus(scope.row)">
                      {{ scope.row.enabled ? '禁用' : '启用' }}
                    </el-button>
                    <el-button link type="danger" size="small" @click="deleteRule(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>

          <div class="pagination-container">
            <el-pagination
                v-model:current-page="page.pageNum"
                v-model:page-size="page.pageSize"
                :page-sizes="[5, 10, 20,30]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total1"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import {addRules, delRules, findAllJia, findAllJian, updateRules} from "@/api/system/points.js";

// 表单数据
const ruleFormRef = ref(null)
const ruleForm = reactive({
  id: null,
  ruleName:'',
  type:'add',
  category:'',
  score:null,
  description: '',
  status: true
})

// 表单验证规则
const rules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择积分分类', trigger: 'change' }
  ],
   score: [
    { required: true, message: '请输入积分数值', trigger: 'blur' },
    { type: 'number', min: 1, message: '积分必须大于0', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入规则描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 编辑状态
const isEdit = ref(false)

// 分类选项
const categoryOptions = ref([
  { value: '述职', label: '述职' },
  { value: '违纪', label: '违纪' },
  { value: '不服管教', label: '不服管教' },
  { value: '礼仪', label: '礼仪' },
  { value: '招生', label: '招生' },
])

// 表格数据
const loading = ref(false)
const rulesData = ref([])
const searchText = ref('')
const activeTab = ref('add')
const page=ref({
  pageSize:5,
  pageNum:1,
  total1:null,
  mhc: null
})
const loadAddRules = async () => {
  loading.value = true;
  try {
    const res = await findAllJia(page.value);
    if (res.data.code === 200) {
      // 字段映射处理
      const list = res.data.data.records.map(item => ({
        id: item.id,
        ruleName: item.ruleName,
        category: item.category,
        score: item.score,
        description: item.description,
        status: item.status,
        type: 'add'
      }));
      rulesData.value = list;
      page.value.total1 = res.data.data.total || 0;
    } else {
      ElMessage.error("加载加分规则失败");
    }
  } catch (error) {
    ElMessage.error("网络错误，请稍后再试");
  } finally {
    loading.value = false;
  }
};


const loadDeductRules = async () => {
  loading.value = true;
  try {
    const res = await findAllJian(page.value);
    if (res.data.code === 200) {
      // 做字段映射处理，统一为 ruleName/score/status
      const list = res.data.data.records.map(item => ({
        id: item.id,
        ruleName: item.ruleName,     // name -> ruleName
        category: item.category,
        score: item.score,      // points -> score
        description: item.description,
        status: item.status,    // enabled -> status
        type: 'deduct'
      }));
      rulesData.value = list;
      page.value.total1 = res.data.data.total || 0;
    } else {
      ElMessage.error("加载扣分规则失败");
    }
  } catch (error) {
    ElMessage.error("网络错误，请稍后再试");
  } finally {
    loading.value = false;
  }
};



// ✅ 定义 loadCurrentTabRules 在 loadAddRules / loadDeductRules 之后
const loadCurrentTabRules = async () => {
  if (activeTab.value === "add") {
    await loadAddRules();
  } else if (activeTab.value === "deduct") {
    await loadDeductRules();
  } else {
    console.warn("未知的 activeTab 值:", activeTab.value);
    activeTab.value = "add"; // 回退到默认 tab
    await loadAddRules();
  }
};


// ✅ watch 放在 loadCurrentTabRules 之后
watch(() => page.value.pageNum, loadCurrentTabRules);
watch(() => page.value.pageSize, loadCurrentTabRules);




const addRulesData = computed(() => {
  return rulesData.value.filter(item => item.type === 'add');
});

const deductRulesData = computed(() => {
  return rulesData.value.filter(item => item.type === 'deduct');
});

onMounted(async () => {
  await loadCurrentTabRules(); // 初始化加载当前 tab 数据
});

// 监听tab变化
watch(() => activeTab.value, async (newVal) => {
  page.value.pageNum = 1;
  if (newVal === 'add') {
    await loadAddRules();
  } else if (newVal === 'deduct') {
    await loadDeductRules();
  }
});




const searchRules = () => {
  page.value.pageNum = 1
  loadCurrentTabRules()
}


const filterRules = () => {
  page.value.pageNum = 1
  loadCurrentTabRules()
}


const refreshTable = () => {
  page.value.mhc = null
  loadCurrentTabRules()
}

const handleTabClick = (tab) => {
  page.value.pageNum = 1
  loadCurrentTabRules()
}


const handleSizeChange = (val) => {
  page.value.pageSize = val
  loadCurrentTabRules()
}

const handleCurrentChange = (val) => {
  page.value.pageNum = val
  loadCurrentTabRules()
}
const formatFormData = () => {
  const score = ruleForm.score;
  const finalScore = ruleForm.type === 'deduct' ? -Math.abs(score) : Math.abs(score);

  return {
    ...ruleForm,
    score: finalScore,
    type: ruleForm.type === 'add' ? 1 : 0,
    status: ruleForm.status ? 1 : 0
  };
};


const submitForm = (formEl) => {
  if (!formEl) return;

  formEl.validate(async (valid) => {
    if (valid) {
      try {
        let res;

        if (!isEdit.value) {
          // 添加规则
          res = await addRules(formatFormData());
        } else {
          // 更新规则
          res = await updateRules(formatFormData());

        }

        if (res.data.code === 200) {
          ElMessage.success(isEdit.value ? '更新成功' : '添加成功');
          resetForm(formEl);
          isEdit.value = false;
          loadCurrentTabRules(); // 刷新表格
        } else {
          ElMessage.error(res.data.msg || '操作失败');
        }
      } catch (error) {
        ElMessage.error('请求失败，请检查网络或服务器');
        console.error(error);
      }
    } else {
      ElMessage.warning('请正确填写所有字段');
      return false;
    }
  });
};



const resetForm = (formEl) => {
  if (!formEl) return;
  formEl.resetFields();
  ruleForm.id = null;
  ruleForm.type = 'add';
  ruleForm.score = null;
  ruleForm.status = true;
  isEdit.value = false;
};


const editRule = (row) => {
  isEdit.value = true
  Object.keys(ruleForm).forEach(key => {
    if (key in row) {
      ruleForm[key] = row[key]
    }
  })
}

const cancelEdit = () => {
  resetForm(ruleFormRef.value)
  isEdit.value = false
}

const toggleRuleStatus = (row) => {
  const index = rulesData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    rulesData.value[index].enabled = !rulesData.value[index].enabled
    ElMessage.success(`规则已${rulesData.value[index].enabled ? '启用' : '禁用'}`)
  }
}

const deleteRule = async (row) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除规则 "${row.ruleName}" 吗？`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    );

    // 发起删除请求
    const res = await delRules(row.id);

    if (res.data.code === 200) {
      ElMessage.success('规则删除成功');

      // 刷新当前 tab 数据
      loadCurrentTabRules();
    } else {
      ElMessage.error(res.data.msg || '删除失败');
    }
  } catch (error) {
    ElMessage.info('已取消删除');
    console.error(error);
  }
};

</script>



<style scoped>
.points-rules-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.points-rules-container::-webkit-scrollbar {
  display: none;
}

.form-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible; /* 改为visible，避免内容被截断 */
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
  height: auto; /* 改为auto，让内容决定高度 */
  display: flex;
  flex-direction: column;
}

.form-card:hover, .table-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px; /* 添加底部边距 */
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: visible; /* 改为visible，避免内容被截断 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

:deep(.el-tabs__content::-webkit-scrollbar) {
  display: none;
}

/* 确保行布局正确 */
.el-row {
  width: 100%;
  margin-bottom: 30px; /* 添加底部边距 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .el-form-item {
    margin-bottom: 22px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .el-row {
    flex-direction: column;
  }
  
  .el-col {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
  
  .form-card, .table-card {
    height: auto;
    margin-bottom: 20px;
  }
}
</style>