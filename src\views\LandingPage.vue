<template>
  <div class="landing-page">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="logo-container">
        <img src="/images/logo.png" alt="振涛教育" class="logo" />
        <b>云计算学院积分管理系统</b>
      </div>
      <div class="nav-links">
        <a href="#about">关于学院</a>
        <a href="#features">教学特色</a>
        <a href="#career">就业前景</a>
        <a href="#awards">荣誉成果</a>
        <a @click.prevent="goToPointsRules" style="cursor: pointer">积分规则</a>
        <el-button type="primary" class="login-btn" @click="goToLogin">登录系统</el-button>
      </div>
    </header>

    <!-- 走马灯 -->
    <div class="carousel-section">
      <el-carousel :interval="5000" type="card" height="500px" indicator-position="outside">
        <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
          <div class="carousel-content" :style="{ backgroundImage: `url(${item.image})` }">
            <div class="carousel-overlay">
              <h2>{{ item.title }}</h2>
              <p>{{ item.description }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 关于学院 -->
    <section id="about" class="section about-section">
      <!-- 排行榜区域 -->
      <h2 class="section-title">云计算积分排行榜</h2>
      <div class="rankings-section">
        <el-row gutter="50">
          <el-col :xs="24" :sm="8">
            <el-card class="ranking-card">
              <h3 class="ranking-title">学生积分前10名</h3>
              <ol class="ranking-list">
                <li v-for="(stu, idx) in topStudents" :key="stu.id">
                  <span class="rank-index">{{ idx + 1 }}</span>
                  <span class="rank-name">{{ stu.realName }}</span>
                  <span class="rank-score rank-score-green">{{ stu.points }}分</span>
                </li>
              </ol>
              <!-- 加分消息轮播 -->
              <div class="score-marquee score-marquee-green">
                <el-carousel
                  :interval="2500"
                  arrow="never"
                  indicator-position="none"
                  height="36px"
                  direction="vertical"
                  :autoplay="true"
                  class="score-marquee add"
                >
                  <el-carousel-item v-for="(msg, idx) in addScoreMessages" :key="idx">
                    <span class="marquee-icon add-icon">⬆️</span>
                    <div v-if="addScoreMessages.length === 0">加载中...</div>
                    <span class="marquee-content add-content"
                      >{{ msg.studentName }} 获得加分 {{ msg.points }}分，{{ msg.reason }}</span
                    >
                  </el-carousel-item>
                </el-carousel>
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card class="ranking-card">
              <h3 class="ranking-title">班级平均积分前三名</h3>
              <ol class="ranking-list">
                <li v-for="(cls, idx) in topClasses" :key="cls.id">
                  <span class="rank-index">{{ idx + 1 }}</span>
                  <span class="rank-name">{{ cls.className }}</span>
                  <span class="rank-score rank-score-blue">{{ cls.points }}分</span>
                </li>
              </ol>
              <!-- 班级榜下方不显示轮播 -->
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card class="ranking-card">
              <h3 class="ranking-title">学生积分后10名</h3>
              <ol class="ranking-list">
                <li v-for="(stu, idx) in bottomStudents" :key="stu.id">
                  <span class="rank-index">{{ idx + 1 }}</span>
                  <span class="rank-name">{{ stu.realName }}</span>
                  <span class="rank-score rank-score-red">{{ stu.points }}分</span>
                </li>
              </ol>
              <!-- 减分消息轮播 -->
              <div class="score-marquee score-marquee-red">
                <el-carousel
                  :interval="2500"
                  arrow="never"
                  indicator-position="none"
                  height="36px"
                  direction="vertical"
                  class="score-marquee subtract"
                  :autoplay="true"
                >
                  <el-carousel-item v-for="(msg, idx) in deductScoreMessages" :key="idx">
                    <span class="marquee-icon subtract-icon">⬇️</span>
                    <div v-if="deductScoreMessages.length === 0">加载中...</div>
                    <span class="marquee-content subtract-content"
                      >{{ msg.studentName }} 被扣分 {{ msg.points }}分，{{ msg.reason }}</span
                    >
                  </el-carousel-item>
                </el-carousel>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 积分消息 -->
      <div class="score-notice-carousel">
        <el-carousel
          :interval="3000"
          arrow="never"
          indicator-position="none"
          height="48px"
          class="score-carousel"
          direction="vertical"
          :autoplay="true"
        >
          <el-carousel-item v-for="(msg, idx) in scoreMessages" :key="idx">
            <div class="score-notice-item" :class="msg.pointsChange">
              <el-icon class="notice-icon" style="color: #409eff; margin-right: 8px"
                ><Promotion
              /></el-icon>
              <span class="notice-content"
                >{{ msg.studentName.substring(0, msg.studentName.length - 2) }}** &nbsp;{{
                  msg.reason
                }}</span
              >
              <span class="notice-score" :class="msg.pointsChange === 1 ? 'add' : 'sub'">
                {{ msg.pointsChange === 1 ? '+' : '-'
                }}{{
                  msg.pointsChange === 2
                    ? msg.pointsBefore - msg.pointsAfter
                    : msg.pointsAfter - msg.pointsBefore
                }}
              </span>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>

      <div class="container">
        <h2 class="section-title">关于振涛教育云计算学院</h2>
        <div class="section-content">
          <div class="text-content">
            <p>
              振涛教育云计算学院是国内领先的专业云计算技术培训机构，致力于培养高端云计算人才，为学生提供系统化、专业化的云计算技术培训。学院拥有一支经验丰富的教师团队，采用"理论+实践"的教学模式，确保学生能够掌握云计算领域的前沿技术和实用技能。
            </p>
            <p>
              云计算专业作为培养云计算和大数据相关岗位的专业人才而成立的技术专业。以Java为基础，以云计算和大数据技术为核心，结合AI大模型，打造高端技术产业，培养“云+AI”时代高端技术人才。在企业级应用开发领域，Java为当之无愧的王者编程语言，选择振涛云计算，人生注定不平凡。
            </p>
            <div class="stats">
              <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">就业率</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">学院学员</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">AI教学</div>
              </div>
            </div>
          </div>
          <div class="image-content">
            <img src="/images/zhentao/yuna.jpg" alt="云计算奋斗" class="about-image" />
          </div>
        </div>
      </div>
    </section>

    <!-- 教学特色 -->
    <section id="features" class="section features-section">
      <div class="container">
        <h2 class="section-title">教学特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <el-icon class="feature-icon"><Monitor /></el-icon>
            <h3>前沿技术课程</h3>
            <p>
              以java编程语言为主，通过在校学习，掌握数据库、Java全栈、分布式、微服务、百万级数据存储与处理等专业知识内容，通过企业级项目开发掌握应用技能，增强动手开发能力，从0到1实现大型项目开发能力。AI助力编程，让学习更简单。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon"><Connection /></el-icon>
            <h3>实战项目驱动</h3>
            <p>
              通过项目的实战，全程授以学生思维模型培养课程，让学生不仅掌握专业技能，同时能收获为人处世之道，面对问题，不退缩迎难而上，培养学生快速解决问题的能力。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon"><User /></el-icon>
            <h3>师资力量</h3>
            <p>
              项目经理团队和就业指导团队都来自互联网等大厂。着眼行业未来，规划部署“云+AI”所需，让学生站在科技前沿。讲师团队更是具有“双师型”资格且有丰富的教学和项目经验。强大师资力量保障，助力学生优质就业。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon"><Promotion /></el-icon>
            <h3>就业保障</h3>
            <p>
              自成立以来，云计算目前已有多个平均薪资优秀班级，部分优异学生达到了高质量就业。云计算就业形势一片大好，选择云计算专业，必将铸就辉煌人生！
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon"><Cpu /></el-icon>
            <h3>AI伴学</h3>
            <p>
              通过 AI 伴学，学员相当于拥有 “永不离线的技术导师 + 专属学习管家”，将技术学习的
              “不确定性” 转化为 “可量化的成长路径”，从容应对 Java
              全栈、分布式架构等复杂知识的学习挑战，真正实现 “效率与深度双突破”。
            </p>
          </div>
          <div class="feature-card">
            <el-icon class="feature-icon"><Medal /></el-icon>
            <h3>学院院长</h3>
            <p>
              云计算专业院长郭溪溪以引领新时代、塑造新格局、精锐聚强师、硬核赢未来四方面内容讲述AI浪潮下云计算专业改革征途。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 就业前景 -->
    <section id="career" class="section career-section">
      <div class="container">
        <h2 class="section-title">高薪就业前景</h2>
        <div class="section-content">
          <div class="image-content">
            <div class="career-image-container">
              <div class="career-image-wrapper">
                <img src="/images/zhentao/yunjy.png" alt="云计算就业" class="career-image" />
              </div>
            </div>
          </div>
          <div class="text-content">
            <p class="highlight-text">
              云计算行业正处于蓬勃发展阶段，人才需求量大，薪资水平高。我们的毕业生平均起薪达到18K+，就业率高达100%。踏入云计算领域，就像闯入一片待垦的数字新大陆。在这里，我们解码分布式系统的奥秘，驯服海量数据的洪流，让算力像空气般触手可及。每攻克一个技术难题，每实现一次高效部署，都是在拓宽科技的深度与广度。云计算人以码为犁、以智为种，耕耘出属于未来的科技粮仓，这份对技术的执着，永远滚烫
              。
            </p>
            <div class="career-paths">
              <div class="career-path">
                <h3>云平台架构师</h3>
                <p>平均薪资: 25K-50K</p>
              </div>
              <div class="career-path">
                <h3>云原生开发工程师</h3>
                <p>平均薪资: 18K-35K</p>
              </div>
              <div class="career-path">
                <h3>DevOps工程师</h3>
                <p>平均薪资: 20K-40K</p>
              </div>
              <div class="career-path">
                <h3>Java+AI</h3>
                <p>平均薪资: 18K-45K</p>
              </div>
            </div>
            <div class="companies">
              <p class="companies-title">岗位培养方向:</p>
              <p>
                Java开发工程师、云计算开发工程师、架构师、项目经理、大数据开发工程师、高级数据分析师等。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 荣誉成果 -->
    <section id="awards" class="section awards-section">
      <div class="container">
        <h2 class="section-title">荣誉成果</h2>
        <div class="section-content">
          <div class="text-content">
            <p>
              振涛教育云计算学院凭借优质的教学质量和出色的就业成果，获得了多项荣誉和认可。我们的学生在各类技能大赛中屡获佳绩，展现了扎实的专业功底和创新能力。
            </p>
            <ul class="awards-list">
              <li>云计算连续三年获得"中国IT教育机构50强"称号</li>
              <li>云计算荣获"最具就业竞争力IT培训机构"奖项</li>
              <li>学生团队在学校“一面二就”大赛多次拿下第一名</li>
              <li>被评为"云计算人才培养示范基地"</li>
              <li>未来已来，决胜AI，全面实现AI化教育</li>
            </ul>
          </div>
          <div class="image-content">
            <img src="/images/zhentao/yunJL.jpg" alt="云计算奖项" class="awards-image" />
          </div>
        </div>
      </div>
    </section>

    <!-- 底部信息 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/images/logo.png" alt="振涛教育" class="footer-logo-img" />
            <p>振涛教育云计算学院</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h3>关于我们</h3>
              <a @click.prevent="goToPage('/about')">学院简介</a>
              <a @click.prevent="goToPage('/teachers')">师资力量</a>
              <a @click.prevent="goToPage('/history')">发展历程</a>
              <a @click.prevent="goToPage('/news')">新闻动态</a>
            </div>
            <div class="footer-column">
              <!--              <h3>课程中心</h3>-->
              <!--              <a @click.prevent="goToPage('/course-basic')">云计算基础</a>-->
              <!--              <a @click.prevent="goToPage('/course-platform')">云平台运维</a>-->
              <!--              <a @click.prevent="goToPage('/course-dev')">云原生开发</a>-->
              <!--              <a @click.prevent="goToPage('/course-bigdata')">大数据分析</a>-->
              <h3>振涛教育微信公众号</h3>
              <el-image src="../../public/img.png" style="width: 50%" />
            </div>
            <div class="footer-column">
              <!--              <h3>学员服务</h3>-->
              <!--              <a @click.prevent="goToPage('/job')">就业指导</a>-->
              <!--              <a @click.prevent="goToPage('/works')">学员作品</a>-->
              <!--              <a @click.prevent="goToPage('/resources')">学习资源</a>-->
              <!--              <a @click.prevent="goToPage('/faq')">常见问题</a>-->
              <h3>振涛教育官方微信号</h3>
              <el-image src="../../public/img_1.png" style="width: 50%" />
            </div>
          </div>
        </div>
        <div class="copyright">
          <p>© 2025 振涛教育云计算学院 版权所有</p>
        </div>
      </div>
    </footer>

    <!--    &lt;!&ndash; 活动报名模态框入口 &ndash;&gt;-->
    <!--    <el-button class="activity-modal-fab" type="success" @click="activityDialogVisible = true">活动报名</el-button>-->
    <!--    <el-dialog v-model="activityDialogVisible" title="活动报名入口" width="350px" append-to-body>-->
    <!--      <div style="text-align:center;">-->
    <!--        <el-button type="primary" size="large" @click="goToActivitySignup">进入活动报名</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->

    <!-- 右侧悬浮快捷入口（小三角悬浮展开） -->
    <div class="side-toolbar-wrapper" @mouseenter="openToolbar" @mouseleave="closeToolbar">
      <transition name="fade-slide">
        <div v-if="toolbarOpen" class="side-toolbar">
          <div class="toolbar-item" @click="goToHotActivity">
            <el-icon><Promotion /></el-icon>
            <span>热门活动</span>
          </div>
          <!--          <div class="toolbar-item activity-signup-btn" @click="activityDialogVisible = true">-->
          <!--            <el-icon><User /></el-icon>-->
          <!--            <span>活动报名</span>-->
          <!--          </div>-->
          <div class="toolbar-item" @click="goToVideoCenter">
            <el-icon><VideoCamera /></el-icon>
            <span>视频中心</span>
          </div>
          <div class="toolbar-item" @click="goToHonorCenter">
            <el-icon><Trophy /></el-icon>
            <span>荣誉中心</span>
          </div>
          <div class="toolbar-item" @click="contactDialogVisible = true">
            <el-icon><User /></el-icon>
            <span>联系我们</span>
          </div>
        </div>
      </transition>
      <div class="side-triangle" :class="{ open: toolbarOpen }"></div>
    </div>
    <el-dialog v-model="contactDialogVisible" title="联系我们" width="350px" append-to-body>
      <div style="text-align: center">
        <p>电话: 133-4312-2949</p>
        <p>邮箱: <EMAIL></p>
        <p>地址: 河北省保定市振涛教育</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {onUnmounted, ref} from 'vue';
  import { useRouter } from 'vue-router';
  import {
    Monitor,
    Connection,
    User,
    Promotion,
    Cpu,
    Medal,
    VideoCamera,
    Trophy
  } from '@element-plus/icons-vue';
  import axios from 'axios';
  import { queryBottomTenStudents, queryTopTenStudents } from '@/api/system/student.js';
  import { queryTopThreeClass } from '@/api/system/class.js';
  import { queryTodayAddPoints, queryTodayMinusPoints } from '@/api/system/points.js';

  const router = useRouter();

  // 学生加减分消息通知数据
  const scoreMessages = ref();

  const scoreMessagesRef = () => {
    axios({
      url: '/api/points-record/findAll',
      method: 'post'
    }).then((res) => {
      if (res.status == 200) {
        scoreMessages.value = res.data;
      }
    });
  };
  scoreMessagesRef();

  // 走马灯数据
  const carouselItems = [
    {
      image: '/images/zhentao/yun1.png',
      title: '云计算技术培训领导者',
      description: '专注云计算人才培养，打造IT精英'
    },
    {
      image: '/images/zhentao/yun2.png',
      title: '实战驱动，项目引领',
      description: '真实项目实训，无缝对接企业需求'
    },
    {
      image: '/images/zhentao/yun3.png',
      title: '高薪就业保障',
      description: '100%就业率，平均薪资18K+'
    }
  ];
  const intervalIds=ref([]);
  // mock 排行榜数据
  const topStudents = ref([]);
  const findTopStudents = () => {
    queryTopTenStudents().then((res) => {
      // 统一字段名
      topStudents.value = res.data.data;
    });
    const id=setInterval(()=>{
      queryTopTenStudents().then((res) => {
        // 统一字段名
        topStudents.value = res.data.data;
      });
    },5000)
    intervalIds.value.push(id)
  };
  findTopStudents();
  const bottomStudents = ref([]);
  const findBottomStudents = () => {
    queryBottomTenStudents().then((res) => {
      // 统一字段名
      bottomStudents.value = res.data.data;
    });
   const id=setInterval(()=>{
     queryBottomTenStudents().then((res) => {
       bottomStudents.value = res.data.data;
     });
   },5000)
    intervalIds.value.push(id)
  };
  findBottomStudents();
  const topClasses = ref([]);
  const findTopClasses = () => {
    queryTopThreeClass().then((res) => {
      // 统一字段名
      topClasses.value = res.data.data;
    });
    const id=setInterval(()=>{
      queryTopThreeClass().then((res) => {
        // 统一字段名
        topClasses.value = res.data.data;
      });
    },5000)
    intervalIds.value.push(id)
  };
  findTopClasses();
  // mock 加分消息
  const addScoreMessages = ref([]);
  const findAddScoreMessages = () => {
    queryTodayAddPoints().then((res) => {
      // 统一字段名
      addScoreMessages.value = res.data.data;
    });
    const id=setInterval(()=>{
      queryTodayAddPoints().then((res) => {
        // 统一字段名
        addScoreMessages.value = res.data.data;
      });
    },5000)
   intervalIds.value.push(id)
  };
  findAddScoreMessages();
  // mock 扣分消息
  const deductScoreMessages = ref([]);
  const findDeductScoreMessages = () => {
    queryTodayMinusPoints().then((res) => {
      // 统一字段名
      deductScoreMessages.value = res.data.data;
    });
    const id=setInterval(()=>{
      queryTodayMinusPoints().then((res) => {
        // 统一字段名
        deductScoreMessages.value = res.data.data;
      });
    },5000)
    intervalIds.value.push(id)
  };
  findDeductScoreMessages();
  onUnmounted(() => {
    intervalIds.value.forEach(id => clearInterval(id));
    intervalIds.value = []; // 清空ID数组
  });
  // 跳转到登录页
  const goToLogin = () => {
    router.push('/login');
  };

  const activityDialogVisible = ref(false);

  const goToPointsRules = () => {
    router.push('/points-rules');
  };
  const goToActivitySignup = () => {
    activityDialogVisible.value = false;
    router.push('/activity-signup');
  };

  const contactDialogVisible = ref(false);
  const goToHotActivity = () => {
    router.push('/activity-signup');
  };
  const goToVideoCenter = () => {
    window.open(
      'https://www.douyin.com/user/MS4wLjABAAAAftw_WbVjIXErZwyh7L5vfhtXHcI1sPE6rbiwZLL95O_8tzW47jEsaaEM2KQY0Bdd?from_tab_name=main'
    );
  };
  const goToHonorCenter = () => {
    router.push('/awards');
  };
  let toolbarOpen = ref(false);
  let closeTimer = null;

  function openToolbar() {
    if (closeTimer) {
      clearTimeout(closeTimer);
      closeTimer = null;
    }
    toolbarOpen.value = true;
  }
  function closeToolbar() {
    closeTimer = setTimeout(() => {
      toolbarOpen.value = false;
    }, 300);
  }

  const goToPage = (path) => {
    router.push(path);
  };
</script>

<style scoped>
  .score-notice-carousel {
    width: 100%;
    max-width: 700px;
    margin: 32px auto 0 auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 0 16px;
  }
  .score-carousel {
    width: 100%;
    height: 48px;
    line-height: 48px;
  }
  .score-notice-item {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    height: 48px;
    justify-content: space-between;
  }
  .notice-icon {
    font-size: 20px;
    flex-shrink: 0;
  }
  .notice-content {
    flex: 1;
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .notice-score {
    font-size: 15px;
    font-weight: 500;
    margin-left: 12px;
    padding: 2px 10px;
    border-radius: 12px;
    background: #f5f7fa;
    min-width: 40px;
    text-align: center;
    transition: background 0.2s;
  }
  .notice-score.add {
    color: #67c23a;
    background: #e9f9ec;
  }
  .notice-score.sub {
    color: #f56c6c;
    background: #fef0f0;
  }

  /* 全局样式 */
  .landing-page {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: #333;
    line-height: 1.6;
    background-color: #f9fcff;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section {
    padding: 80px 0;
  }

  .section-title {
    text-align: center;
    font-size: 36px;
    color: #1a4f8c;
    margin-bottom: 50px;
    position: relative;
  }

  .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #1a4f8c, #3c9af0);
    border-radius: 2px;
  }

  .section-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
  }

  /* 头部导航 */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 22px 60px 22px 60px;
    background: linear-gradient(90deg, #f9fcff 60%, #eaf3ff 100%);
    box-shadow: 0 4px 18px rgba(60, 154, 240, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1.5px solid #e6eaf0;
  }
  .logo-container {
    font-size: 28px;
    color: #1a4f8c;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .logo {
    height: 62px;
    margin-right: 18px;
    transition: transform 0.2s;
  }
  .logo:hover {
    transform: scale(1.07) rotate(-2deg);
  }
  .nav-links {
    display: flex;
    align-items: center;
    gap: 44px;
  }
  .nav-links a {
    text-decoration: none;
    color: #1a4f8c;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.25s;
    padding: 4px 10px;
    border-radius: 8px;
  }
  .nav-links a:hover {
    background: linear-gradient(90deg, #eaf3ff 60%, #d0e6ff 100%);
    color: #1989fa;
  }
  .login-btn {
    background: linear-gradient(90deg, #1a4f8c, #3c9af0);
    border: none;
    font-weight: 500;
    border-radius: 22px;
    padding: 8px 28px;
    font-size: 17px;
    box-shadow: 0 2px 8px rgba(60, 154, 240, 0.1);
    margin-left: 18px;
    transition:
      background 0.2s,
      box-shadow 0.2s;
  }
  .login-btn:hover {
    background: linear-gradient(90deg, #3c9af0, #1a4f8c);
    box-shadow: 0 4px 16px rgba(60, 154, 240, 0.18);
  }

  /* 走马灯部分 */
  .carousel-section {
    margin-top: 90px;
    padding: 0;
  }

  .carousel-content {
    height: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
  }

  .carousel-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: #fff;
  }

  .carousel-overlay h2 {
    font-size: 32px;
    margin-bottom: 10px;
  }

  .carousel-overlay p {
    font-size: 18px;
    opacity: 0.9;
  }

  /* 关于学院部分 */
  .about-section {
    background-color: #fff;
  }

  .about-section .text-content {
    flex: 1;
  }

  .about-section p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.8;
  }

  .about-image {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .stats {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #1a4f8c;
    margin-bottom: 5px;
  }

  .stat-label {
    font-size: 16px;
    color: #666;
  }

  /* 教学特色部分 */
  .features-section {
    background-color: #f0f7ff;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }

  .feature-card {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    text-align: center;
  }

  .feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(60, 154, 240, 0.1);
  }

  .feature-icon {
    font-size: 40px;
    color: #3c9af0;
    margin-bottom: 20px;
  }

  .feature-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #1a4f8c;
  }

  .feature-card p {
    font-size: 15px;
    color: #666;
  }

  /* 就业前景部分 */
  .career-section {
    background-color: #fff;
  }

  .career-section .text-content {
    flex: 1;
  }

  .highlight-text {
    font-size: 18px;
    color: #1a4f8c;
    margin-bottom: 30px;
    line-height: 1.8;
  }

  .career-paths {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }

  .career-path {
    background-color: #f0f7ff;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
  }

  .career-path:hover {
    background-color: #e0f0ff;
  }

  .career-path h3 {
    font-size: 18px;
    color: #1a4f8c;
    margin-bottom: 5px;
  }

  .career-path p {
    font-size: 16px;
    color: #3c9af0;
    font-weight: 500;
  }

  .companies-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .company-logos {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  .company {
    padding: 8px 15px;
    background-color: #f0f7ff;
    border-radius: 20px;
    font-size: 14px;
    color: #1a4f8c;
  }

  .career-image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
  }

  .career-image-wrapper {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .career-image {
    width: 100%;
    display: block;
  }

  /* 荣誉成果部分 */
  .awards-section {
    background-color: #f0f7ff;
  }

  .awards-section .text-content {
    flex: 1;
  }

  .awards-section p {
    font-size: 16px;
    line-height: 1.8;
    margin-bottom: 20px;
  }

  .awards-list {
    padding-left: 20px;
  }

  .awards-list li {
    margin-bottom: 15px;
    font-size: 16px;
    position: relative;
    padding-left: 15px;
  }

  .awards-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 6px;
    height: 6px;
    background-color: #3c9af0;
    border-radius: 50%;
  }

  .awards-image {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  /* 底部部分 */
  .footer {
    background-color: #1a4f8c;
    color: #fff;
    padding: 60px 0 20px;
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
  }

  .footer-logo {
    margin-right: 40px;
  }

  .footer-logo-img {
    height: 100px;
    margin-bottom: 15px;
  }

  .footer-logo p {
    font-size: 16px;
    opacity: 0.8;
  }

  .footer-links {
    display: flex;
    flex: 1;
    justify-content: space-between;
  }

  .footer-column h3 {
    font-size: 18px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
  }

  .footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: #3c9af0;
  }

  .footer-column a,
  .footer-column p {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    text-decoration: none;
    transition: all 0.3s;
  }

  .footer-column a:hover {
    color: #fff;
    transform: translateX(5px);
  }

  .copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    opacity: 0.7;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .section-content {
      flex-direction: column;
    }

    .features-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .about-image,
    .career-image-container,
    .awards-image {
      margin-top: 30px;
    }
  }

  @media (max-width: 768px) {
    .header {
      padding: 15px 20px;
    }

    .nav-links {
      display: none;
    }

    .section {
      padding: 60px 0;
    }

    .section-title {
      font-size: 28px;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .career-paths {
      grid-template-columns: 1fr;
    }

    .footer-content {
      flex-direction: column;
    }

    .footer-logo {
      margin-bottom: 30px;
    }

    .footer-links {
      flex-wrap: wrap;
    }

    .footer-column {
      width: 50%;
      margin-bottom: 30px;
    }
  }

  /* 自定义走马灯样式 */
  :deep(.el-carousel__item) {
    border-radius: 8px;
  }

  :deep(.el-carousel__item:not(.is-active)) {
    filter: brightness(0.7) blur(2px);
    transform: scale(0.85);
  }

  :deep(.el-carousel__arrow) {
    background-color: rgba(26, 79, 140, 0.7);
    border-radius: 50%;
    width: 44px;
    height: 44px;
  }

  :deep(.el-carousel__indicators) {
    transform: translateY(15px);
  }

  :deep(.el-carousel__indicator) {
    padding: 0 8px;
  }

  :deep(.el-carousel__indicator--active button) {
    background-color: #1a4f8c;
  }

  :deep(.el-carousel__button) {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(26, 79, 140, 0.3);
  }

  .activity-modal-fab {
    position: fixed;
    top: 120px;
    right: 40px;
    z-index: 2000;
    box-shadow: 0 2px 10px rgba(60, 154, 240, 0.15);
    border-radius: 24px;
    padding: 16px 24px;
    font-size: 16px;
  }

  .side-toolbar {
    position: fixed;
    top: 60%;
    right: 30px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 18px;
    transform: translateY(-50%);
  }
  .toolbar-item {
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(60, 154, 240, 0.12);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
    color: #1a4f8c;
    border: 1px solid #e6eaf0;
  }
  .toolbar-item:hover {
    background: linear-gradient(90deg, #1a4f8c, #3c9af0);
    color: #fff;
  }
  .toolbar-item .el-icon {
    font-size: 24px;
    margin-bottom: 4px;
  }
  .activity-signup-btn {
    /* 取消默认高亮，仅在悬停时高亮 */
    background: #fff;
    color: #1a4f8c;
    font-weight: bold;
  }
  .activity-signup-btn:hover {
    background: linear-gradient(90deg, #1989fa, #67c23a);
    color: #fff;
  }
  .side-toolbar-wrapper {
    position: fixed;
    top: 60%;
    right: 30px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    transform: translateY(-50%);
  }
  .side-triangle {
    width: 0;
    height: 0;
    border-top: 18px solid transparent;
    border-bottom: 18px solid transparent;
    border-left: 22px solid #1989fa;
    margin-right: 2px;
    cursor: pointer;
    transition: border-left-color 0.2s;
  }
  .side-triangle.open {
    border-left-color: #67c23a;
  }
  .fade-slide-enter-active,
  .fade-slide-leave-active {
    transition: all 0.25s cubic-bezier(0.55, 0, 0.1, 1);
  }
  .fade-slide-enter-from,
  .fade-slide-leave-to {
    opacity: 0;
    transform: translateX(40px);
  }
  .fade-slide-enter-to,
  .fade-slide-leave-from {
    opacity: 1;
    transform: translateX(0);
  }
  .rankings-section {
    max-width: 1200px;
    margin: 40px auto 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }
  .ranking-card {
    margin-bottom: 80px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(60, 154, 240, 0.08);
    min-height: 600px;
    /* 新增宽度设置 */
    width: 360px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
  }
  .ranking-title {
    text-align: center;
    font-size: 20px;
    color: #1a4f8c;
    margin-bottom: 18px;
    font-weight: bold;
  }
  .ranking-list {
    list-style: none;
    padding: 0 12px;
    margin: 0;
  }
  .ranking-list li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
  }
  .ranking-list li:last-child {
    border-bottom: none;
  }
  .rank-index {
    width: 28px;
    text-align: center;
    font-weight: bold;
    color: #409eff;
  }
  .rank-name {
    flex: 1;
    margin-left: 10px;
    color: #333;
  }
  .rank-score {
    color: #f56c6c;
    font-weight: bold;
    min-width: 60px;
    text-align: right;
  }
  .rank-score-green {
    color: #67c23a;
  }
  .rank-score-blue {
    color: #409eff;
  }
  .rank-score-red {
    color: #f56c6c;
  }
  .score-marquee {
    margin-top: 18px;
    background: #f8fafd;
    border-radius: 8px;
    padding: 0 12px;
    min-height: 36px;
    align-items: center;
    box-shadow: 0 1px 4px rgba(60, 154, 240, 0.06);
  }
  .score-marquee-green {
    border-left: 4px solid #67c23a;
  }
  .score-marquee-red {
    border-left: 4px solid #f56c6c;
  }
  .marquee-icon {
    margin-right: 8px;
    font-size: 18px;
    transition: background-color 0.2s;
  }
  .marquee-content {
    font-size: 15px;
    color: #67c23a;
    font-weight: bold;
  }
  /* 加分样式 */
  .score-marquee.add {
    background: #d4f9e2; /* 浅绿色背景 */
  }
  .marquee-icon.add-icon {
    color: #28a745; /* 加分图标颜色 */
  }
  .marquee-content.add-content {
    color: #28a745; /* 加分文字颜色 */
  }

  /* 减分样式 */
  .score-marquee.subtract {
    background: #ffe6e6; /* 浅红色背景 */
  }
  .marquee-icon.subtract-icon {
    color: #dc3545; /* 减分图标颜色 */
  }
  .marquee-content.subtract-content {
    color: #dc3545; /* 减分文字颜色 */
  }



  /* 减分轮播 - 强制向下滚动 */
  .score-marquee-red .el-carousel__container {
    display: flex;
    flex-direction: column-reverse;
    animation-play-state: running !important;
  }

  /* 覆盖悬停暂停的默认行为 */
  .score-marquee-red .el-carousel:hover .el-carousel__container {
    animation-play-state: running !important;
  }

  /* 初始位置和动画 */
  .score-marquee-red .el-carousel__item {
    transform: translateY(100%) !important;
  }

  .score-marquee-red .el-carousel__item.is-active {
    transform: translateY(0) !important;
  }

  .score-marquee-red .el-carousel__item.is-animating {
    transition: transform 0.5s ease-in-out !important;
  }

</style>
