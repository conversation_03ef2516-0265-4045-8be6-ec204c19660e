package com.zhentao.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhentao.dto.system.system.ActivityDto;
import com.zhentao.mapper.ActivityMapper;
import com.zhentao.pojo.Activity;
import com.zhentao.service.ActivityService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {
    @Autowired
    private ActivityMapper activityMapper;
    @Override
    public Result addActivity(ActivityDto activityDto) {
        if (activityDto != null) {
            Activity activity = new Activity();
            activity.setActivityName(activityDto.getActivityName());
            activity.setActivityText(activityDto.getActivityText());
            activity.setNumber(activityDto.getNumber());
            activity.setStartTime(activityDto.getStartTime());
            activity.setStopTime(activityDto.getStopTime());
            activity.setImage(activityDto.getImage());
            activity.setStatus(1);
            Integer userId = UserContext.getCurrentUser().getUserId();
            activity.setCreateBy(userId);
            activity.setCreateTime(new Date());
            int insert = activityMapper.insert(activity);
            if (insert > 0) {
                return Result.OK("活动添加成功");
            } else {
                return Result.ERROR("活动添加失败");
            }
        } else {
            return Result.ERROR("数据不对");
        }
    }

    @Override
    public Result deleteActivity(Integer activityId) {
        if (activityId != null) {
            int delete = activityMapper.deleteById(activityId);
            if (delete > 0) {
                return Result.OK("活动删除成功");
            } else {
                return Result.ERROR("活动删除失败");
            }
        } else {
            return Result.ERROR("无法删除");
        }
    }

    @Override
    public Result updateActivity(ActivityDto activityDto) {
        if (activityDto != null) {
            Activity activity = activityMapper.selectById(activityDto.getActivityId());
            activity.setId(activityDto.getActivityId());
            activity.setActivityName(activityDto.getActivityName());
            activity.setActivityText(activityDto.getActivityText());
            activity.setNumber(activityDto.getNumber());
            activity.setStartTime(activityDto.getStartTime());
            activity.setStopTime(activityDto.getStopTime());
            activity.setImage(activityDto.getImage());
            activity.setUpdateBy(UserContext.getCurrentUser().getUserId());
            activity.setUpdateTime(new Date());
            int update = activityMapper.updateById(activity);
            if (update > 0) {
                return Result.OK("活动修改成功");
            } else {
                return Result.ERROR("活动修改失败");
            }
        } else {
            return Result.ERROR("数据为空");
        }
    }

    @Override
    public Result findActivity() {
        List<Activity> activities = activityMapper.selectList(null);
        if (activities != null && !activities.isEmpty()) {
            return Result.OK(activities);
        } else {
            return Result.ERROR("没有活动");
        }
    }

    @Override
    public Result changeActivityStatus(Integer activityId, Integer status) {
        if (activityId != null) {
            Activity activity = activityMapper.selectById(activityId);
            if (activity != null) {
                activity.setId(activityId);
                activity.setStatus(status);
                activity.setStopTime(new Date());
                int update = activityMapper.updateById(activity);
                if (update > 0) {
                    return Result.OK("活动状态修改成功");
                } else {
                    return Result.ERROR("活动状态修改失败");
                }
            } else {
                return Result.ERROR("活动不存在");
            }
        } else {
            return Result.ERROR("数据为空");
        }
    }
}
