<template>
  <div class="activity-signup-page">
    <h2 class="activity-title">热门活动</h2>
    <div class="activity-intro">
      欢迎参加振涛教育云计算学院的各类热门活动！这里有丰富的竞赛、讲座、实训和创新项目，助你提升技能、结识伙伴、赢取荣誉。
    </div>
    <div v-if="activities.length > 0" class="activity-list">
      <div class="activity-card" v-for="item in activities" :key="item.id">
        <img :src="item.image" class="activity-img" :alt="item.activityName" />
        <div class="activity-info">
          <h3>活动名称：{{ item.activityName }}</h3>
          <p>描述：{{ item.activityText }}</p>
          <p v-if="item.number !== 0">还需要人数：{{ item.number }}人</p>
          <el-button type="primary" size="small" @click="signup(item)" v-if="item.number !== 0"
            >立即报名</el-button
          >
          <el-tag v-if="item.number === 0" type="danger">人员已满</el-tag>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无活动可报名">
      <el-button type="primary" @click="openNoActivityDialog">我要报名</el-button>
    </el-empty>
    <el-dialog v-model="dialogVisible" title="活动报名" width="400px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="班级" prop="className">
          <el-input v-model="form.className" />
        </el-form-item>
        <el-form-item v-if="!form.activityId">
          <el-alert title="当前无可报名活动，无法提交报名" type="warning" show-icon />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="!form.activityId"
          >确认报名</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
  import { ElMessage } from 'element-plus';
  import { findActivity } from '@/api/system/activity';
  import { signUpActivity } from '@/api/system/activityApplication';

  const activities = ref([]);
  const dialogVisible = ref(false);
  const form = ref({ name: '', phone: '', className: '', activityId: null });
  const formRef = ref();
  const rules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    className: [{ required: true, message: '请输入班级', trigger: 'blur' }]
  };
  let currentActivity = null;
  const intervalIds=ref([]);
  function getActivities() {
    findActivity().then((res) => {
      activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
    });
    const id=setInterval(()=>{
      findActivity().then((res) => {
        activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
      });
    }, 5000)
    intervalIds.value.push(id);
  }
  onUnmounted(() => {
  intervalIds.value.forEach(id => clearInterval(id));
  intervalIds.value = []; // 清空ID数组
});
  function signup(item) {
    form.value = { name: '', phone: '', className: '', activityId: item.id };
    currentActivity = item;
    dialogVisible.value = true;
  }

  function openNoActivityDialog() {
    form.value = { name: '', phone: '', className: '', activityId: null };
    dialogVisible.value = true;
  }

  function submitForm() {
    formRef.value.validate((valid) => {
      if (!valid) return;
      signUpActivity(form.value).then((res) => {
        if (res.data.code === 200) {
          ElMessage.success('报名成功！');
          dialogVisible.value = false;
          if (currentActivity) currentActivity.signed = true;
        } else {
          ElMessage.error(res.data.message || '报名失败');
        }
      });
    });
  }

  onMounted(getActivities);
</script>

<style scoped>
  .activity-signup-page {
    max-width: 1100px;
    margin: 40px auto 60px auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(60, 154, 240, 0.07);
    padding: 40px 32px 32px 32px;
  }
  .activity-title {
    text-align: center;
    font-size: 2.2rem;
    color: #1a4f8c;
    margin-bottom: 24px;
    font-weight: 700;
    letter-spacing: 2px;
  }
  .activity-intro {
    text-align: center;
    font-size: 1.1rem;
    color: #444;
    margin-bottom: 28px;
  }
  .activity-list {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: center;
  }
  .activity-card {
    width: 240px;
    background: #f5f8fc;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(60, 154, 240, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 18px;
    transition:
      box-shadow 0.2s,
      transform 0.2s;
  }
  .activity-card:hover {
    box-shadow: 0 8px 24px rgba(60, 154, 240, 0.16);
    transform: translateY(-4px) scale(1.03);
  }
  .activity-img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-bottom: 1px solid #e6eaf0;
  }
  .activity-info {
    padding: 18px 14px 0 14px;
    text-align: center;
  }
  .activity-info h3 {
    font-size: 1.1rem;
    color: #1989fa;
    margin-bottom: 8px;
    font-weight: 600;
  }
  .activity-info p {
    font-size: 0.98rem;
    color: #444;
    margin-bottom: 14px;
  }
</style>
