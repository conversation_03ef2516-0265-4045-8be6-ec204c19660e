import axios from "axios";
import { ElMessage } from 'element-plus';
import router from '@/router';

const request = axios.create({
    baseURL: '/api',
});

// 添加请求拦截器
request.interceptors.request.use(function (config) {
    // 从localStorage获取token，添加到请求头
    const token = localStorage.getItem("Authorization");
    if (token) {
        config.headers.Authorization = token;
    }
    
    return config;
}, function (error) {
    // 对请求错误做些什么
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
});

// 添加响应拦截器
request.interceptors.response.use(function (response) {
    // 检查是否有新的token需要更新
    const newToken = response.headers['new-token'];
    if (newToken) {
        // 更新localStorage中的token
        localStorage.setItem('Authorization', newToken);
        console.log('Token refreshed');
    }
    
    // 处理特殊情况：字符串响应
    if (typeof response.data === 'string') {
        // 将字符串响应转换为标准格式
        const originalData = response.data;
        if (originalData.includes('成功')) {
            response.data = {
                code: 200,
                message: originalData,
                data: null
            };
        } else {
            response.data = {
                code: 500,
                message: originalData,
                data: null
            };
        }
    }
    
    // 检查响应状态码
    if (response.data && response.data.code !== undefined) {
        if (response.data.code === 200) {
            // 成功响应
            return response;
        } else if (response.data.code === 401) {
            // 未授权
            localStorage.removeItem('Authorization');
            ElMessage.error(response.data.message || '登录已过期，请重新登录');
            router.push('/login');
            return Promise.reject(new Error(response.data.message || '未授权'));
        } else {
            // 其他业务错误
            ElMessage.error(response.data.message || '操作失败');
            return Promise.reject(new Error(response.data.message || '操作失败'));
        }
    }
    
    return response;
}, function (error) {
    // 超出 2xx 范围的状态码都会触发该函数。
    console.error('响应错误:', error);
    
    // 对响应错误做点什么
    if (error.response) {
        // 处理响应错误
        if (error.response.status === 401) {
            // 未授权，清除token并跳转到登录页
            localStorage.removeItem('Authorization');
            ElMessage.error('登录已过期，请重新登录');
            router.push('/login');
        } else if (error.response.status === 403) {
            // 权限不足
            ElMessage.error('权限不足，无法访问');
        } else if (error.response.status === 400) {
            // 请求错误
            ElMessage.error(error.response.data?.message || '请求参数错误，请检查输入');
        } else {
            // 其他错误
            ElMessage.error(error.response.data?.message || '服务器错误');
        }
    } else if (error.request) {
        // 请求已发出，但没有收到响应
        ElMessage.error('网络连接失败，请检查网络');
    } else {
        // 请求配置有误
        ElMessage.error('请求错误: ' + error.message);
    }
    return Promise.reject(error);
});

// 导出request模块
export default request;