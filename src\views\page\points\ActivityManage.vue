<template>
  <div>
    <el-card>
      <div style="margin-bottom: 16px;">
        <el-button type="primary" @click="openDialog()">新增活动</el-button>
      </div>
      <el-table :data="activityList" style="width: 100%">
        <el-table-column prop="activityName" label="活动名称" />
        <el-table-column prop="activityText" label="简介" />
        <el-table-column prop="number" label="需要人数" />
        <el-table-column prop="startTime" label="活动开始时间" />
        <el-table-column prop="stopTime" label="活动结束时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{row}">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '开始' : '结束' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="image" label="活动图片">
          <template #default="{row}">
            <img :src="row.image" style="width: 100px; height: 100px;" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template #default="{row}">
            <el-button size="small" @click="openDialog(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="del(row)">删除</el-button>
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="changeStatus(row)"
              active-text="开始"
              inactive-text="结束"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 新增/编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="form.id ? '编辑活动' : '新增活动'">
      <el-form :model="form" label-width="90px" :rules="rules" ref="formRef">
        <el-form-item label="活动名称" prop="activityName" required>
          <el-input v-model="form.activityName" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="活动内容" prop="activityText" required>
          <el-input v-model="form.activityText" type="textarea" placeholder="请输入活动内容" />
        </el-form-item>
        <el-form-item label="需要人数" prop="number" required>
          <el-input-number v-model="form.number" :min="1" placeholder="请输入需要人数" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" required>
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择开始时间"
          />
        </el-form-item>
        <el-form-item label="活动照片" prop="image">
          <el-upload
            class="activity-image-uploader"
            :action="undefined"
            :show-file-list="false"
            :before-upload="handleImageUpload"
            accept="image/*"
          >
            <template v-if="form.image">
              <img :src="form.image" class="activity-image-preview" />
              <el-button
                class="activity-image-remove"
                type="danger"
                size="small"
                @click.stop.prevent="removeImage"
                plain
              >移除</el-button>
            </template>
            <template v-else>
              <div class="activity-image-upload-btn">
                <el-icon class="plus-icon"><Plus /></el-icon>
                <div class="upload-text">点击上传活动照片</div>
              </div>
            </template>
          </el-upload>
        </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>

    </el-dialog>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { findActivity, addActivity, updateActivity, deleteActivity, changeActivityStatus, uploadActivityImage } from '@/api/system/activity'
import { Plus } from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import dayjs from 'dayjs'

const activityList = ref([])
const dialogVisible = ref(false)
const form = ref({
  id: '',
  activityName:'',
  activityText:'',
  number:0,
  startTime:'',
  image:''
})
const formRef = ref(null)

// 保留中文校验提示
const rules = {
  activityName: [
    { required: true, message: '请输入活动名称', trigger: 'blur' }
  ],
  activityText: [
    { required: true, message: '请输入活动内容', trigger: 'blur' }
  ],
  number: [
    { required: true, message: '请输入需要人数', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ]
}

function getList() {
  findActivity().then(res => {
    activityList.value = res.data.data || []
  })
}
function openDialog(row) {
  form.value = row ? { ...row } : {}
  dialogVisible.value = true
}
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      // 组装参数，字段名与后端一致，时间格式化
      const params = {
        activityId: form.value.id || undefined,
        activityName: form.value.activityName,
        activityText: form.value.activityText,
        number: form.value.number,
        image: form.value.image || '',
        startTime: form.value.startTime ? dayjs(form.value.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
        stopTime: form.value.stopTime ? dayjs(form.value.stopTime).format('YYYY-MM-DD HH:mm:ss') : ''
      }
      const api = form.value.id ? updateActivity : addActivity
      api(params).then(() => {
        ElMessage.success('操作成功')
        dialogVisible.value = false
        getList()
      })
    }
  })
}
function del(row) {
  ElMessageBox.confirm('确定删除该活动吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    deleteActivity(row.id).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  })
}
function changeStatus(row) {
  changeActivityStatus(row.id, row.status).then(() => {
    ElMessage.success('状态已更新')
    getList()
  })
}
function handleImageUpload(file) {
  // 校验
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  // 手动上传
  uploadActivityImage(file).then(res => {
    if (res.data.code === 200) {
      form.value.image = res.data.data
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error(res.data.message || '图片上传失败')
    }
  })
  return false // 阻止el-upload自动上传
}
function removeImage() {
  form.value.imageUrl = ''
}
onMounted(getList)
</script>
<style scoped>
.activity-image-uploader {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.activity-image-upload-btn {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #fafbfc;
}
.activity-image-upload-btn:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
}
.plus-icon {
  font-size: 32px;
  color: #bfbfbf;
}
.upload-text {
  margin-top: 8px;
  color: #888;
  font-size: 14px;
}
.activity-image-preview {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(64,158,255,0.08);
  border: 1px solid #e4e7ed;
  margin-bottom: 8px;
}
.activity-image-remove {
  margin-top: 0;
  margin-left: 0;
}
</style> 