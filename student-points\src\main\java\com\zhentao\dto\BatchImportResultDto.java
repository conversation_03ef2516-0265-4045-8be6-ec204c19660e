package com.zhentao.dto;

import lombok.Data;
import java.util.List;

/**
 * 批量导入结果DTO
 * <AUTHOR>
 */
@Data
public class BatchImportResultDto {
    
    /**
     * 总数据条数
     */
    private Integer totalCount;
    
    /**
     * 成功导入条数
     */
    private Integer successCount;
    
    /**
     * 失败条数
     */
    private Integer failCount;
    
    /**
     * 导入详情
     */
    private List<ImportDetailDto> details;
    
    /**
     * 导入详情DTO
     */
    @Data
    public static class ImportDetailDto {
        /**
         * 班级名称
         */
        private String className;
        
        /**
         * 学生姓名
         */
        private String studentName;
        
        /**
         * 学号
         */
        private String studentNo;
        
        /**
         * 导入状态：success-成功，fail-失败
         */
        private String status;
        
        /**
         * 错误信息（失败时）
         */
        private String errorMessage;
    }
}
