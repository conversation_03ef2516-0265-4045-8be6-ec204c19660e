package com.zhentao;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@SpringBootTest
class StudentPointsApplicationTests {

    @Test
    void contextLoads() {
        BCryptPasswordEncoder bCryptPasswordEncoder=new BCryptPasswordEncoder();
        String user = bCryptPasswordEncoder.encode("user");
        System.out.println(user);
    }
    @Test
    void test() {
        BCryptPasswordEncoder bCryptPasswordEncoder=new BCryptPasswordEncoder();
        String user = bCryptPasswordEncoder.encode("user");
        System.out.println(user);
        System.out.println(bCryptPasswordEncoder.matches("user",user));

    }
}
